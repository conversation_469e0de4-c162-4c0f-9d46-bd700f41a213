# Upshift - Your Personal AI Coach for Meaningful Growth

**Domain:** upshift.life
**Tagline:** "Your Personal AI Coach for Meaningful Growth"

Upshift is a revolutionary personal AI coaching platform designed to unlock your potential and accelerate meaningful growth. Built on the philosophy of "Growth Through Clarity," Upshift transforms the way people approach personal development by providing intelligent, personalized coaching that adapts to your unique journey.

## Technology Stack

- **Frontend:** Flutter 3.8.1+ (Cross-platform mobile development)
- **Backend:** Firebase ecosystem
  - Firebase Core 3.13.1
  - Firebase Auth 5.6.0 (Authentication)
  - Cloud Firestore 5.6.9 (Database)
  - Cloud Functions 5.5.2 (Serverless functions)
- **AI Integration:** Firebase AI 2.2.1 with Google Gemini 2.5 Flash
- **UI Framework:** Material Design with custom theme system
- **State Management:** Provider 6.1.5
- **Local Storage:** Hive CE 2.11.3
- **Chat Interface:** Flutter Chat UI 2.5.2

## Setting up the Upshift Project development environment

### Prerequisites

- Flutter SDK (https://flutter.dev/docs/get-started/install)
- Android Studio (https://developer.android.com/studio)
- Xcode (https://developer.apple.com/xcode/)
- Firebase CLI (https://firebase.google.com/docs/cli)
- FlutterFire CLI (https://firebase.google.com/docs/flutter/setup)
- Git (https://git-scm.com/)

### Installation

1. Clone the repository
2. Install dependencies
   ```bash
   flutter pub get
   ```
3. Run the app
   ```bash
   flutter run
   ```
4. Select the platform you want to run the app on
   ```bash
   flutter emulators
   ```
   You may see the following output:
   ```bash
   2 available devices:
   Medium_Phone_API_36.0 • Medium Phone API 36.0 • Generic      • android
   iPhone 14 Pro Max - ios (iOS 16.2 • Apple • ios • com.apple.CoreSimulator.SimRuntime.iOS-16-2)
   ```
   Run the emulator you want to run the app on
   ```bash
   flutter emulators --launch Medium_Phone_API_36.0
   ```
   To run Android emulator, run the following command:
   ```bash
   flutter run -d pixel
   ```
   To run iOS simulator, run the following command:
   ```bash
   flutter run -d ios
   ```
   To run web, run the following command:
   ```bash
   flutter run -d web
   ```
   To run Linux, run the following command:
   ```bash
   flutter run -d linux
   ```

### Firebase Setup

The Firebase CLI maintains its own concept of an “active project” separate from FlutterFire config. You need to either:
Set that with

```
firebase use --add
```

Deploy with

```
firebase deploy --only firestore:rules
```

### Core Libraries

- Flyer Chat UI (https://pub.dev/packages/flutter_chat_ui)
- Flyer Chat Blog (https://flyer.chat/blog/v2-announcement/)
- Firebase (https://firebase.google.com/)
- Firebase UI (https://pub.dev/packages/firebase_ui_auth)
- Firebase UI OAuth Google (https://pub.dev/packages/firebase_ui_oauth_google)
- Flutter Chat Core (https://pub.dev/packages/flutter_chat_core)
- Flutter Chat UI (https://pub.dev/packages/flutter_chat_ui)
- Flyer Chat File Message (https://pub.dev/packages/flyer_chat_file_message)
- Flutter Local Notifications (https://pub.dev/packages/flutter_local_notifications)
- Flutter Home Widget (https://pub.dev/packages/home_widget)

## Dependencies

### Core Flutter

- **flutter**: Flutter SDK framework
- **cupertino_icons**: iOS-style icons for cross-platform consistency

### Firebase & Backend Services

- **firebase_core**: Firebase initialization and configuration
- **firebase_auth**: User authentication and session management
- **cloud_firestore**: NoSQL database for real-time data synchronization
- **cloud_functions**: Serverless backend functions
- **firebase_ui_auth**: Pre-built authentication UI components
- **firebase_ui_oauth_google**: Google OAuth integration
- **firebase_app_check**: App integrity verification and security
- **firebase_ai**: AI integration with Google Gemini models
- **firebase_remote_config**: Dynamic configuration management

### AI & Chat Interface

- **flutter_chat_core**: Core chat functionality and message handling
- **flutter_chat_ui**: Rich chat UI components with customization
- **flyer_chat_file_message**: File attachment support in chat
- **flyer_chat_image_message**: Image message display and handling
- **flyer_chat_system_message**: System-generated messages
- **flyer_chat_text_message**: Text message rendering
- **flyer_chat_text_stream_message**: Real-time streaming text messages
- **web_socket_channel**: WebSocket communication for real-time features

### UI & Design

- **google_fonts**: Custom typography system
- **flutter_animate**: Smooth animations and transitions
- **font_awesome_flutter**: Comprehensive icon library
- **pull_down_button**: iOS-style contextual menus

### Data & Storage

- **hive_ce**: High-performance local database
- **hive_ce_flutter**: Flutter integration for Hive
- **cross_cache**: Cross-platform caching solution
- **shared_preferences**: Simple key-value storage
- **json_annotation**: JSON serialization annotations

### Utilities

- **provider**: State management and dependency injection
- **uuid**: UUID generation for unique identifiers
- **dio**: Advanced HTTP client with interceptors
- **http**: Basic HTTP requests
- **image_picker**: Camera and gallery image selection
- **file_picker**: File system access and selection
- **url_launcher**: External URL and app launching
- **flutter_dotenv**: Environment variable management
- **intl**: Internationalization and date formatting
- **rxdart**: Reactive programming extensions
- **flutter_lorem**: Lorem ipsum text generation for testing

### Development Tools

- **build_runner**: Code generation automation
- **json_serializable**: Automatic JSON serialization
- **flutter_test**: Unit and widget testing framework
- **integration_test**: End-to-end testing
- **mockito**: Mock object generation for testing
- **flutter_lints**: Dart linting rules
- **flutter_lints_plus**: Additional code quality rules

## Project Architecture

```
lib/
├── api/                    # External API integrations
├── models/                 # Data models and entities
│   ├── models.dart        # All model definitions
│   └── models.g.dart      # Generated JSON serialization code
├── pages/                  # UI screens and pages
│   ├── admin/             # Admin-only pages
│   ├── account.dart       # User account management
│   ├── chat_list_page.dart # Chat list and management
│   ├── chat_page.dart     # AI chat interface
│   ├── email_verification_page.dart
│   ├── guided_path_detail_page.dart
│   ├── home.dart          # Main dashboard
│   ├── login.dart         # Authentication
│   ├── main_navigation.dart # Bottom navigation
│   ├── onboarding.dart    # User onboarding flow
│   ├── path_step_detail_page.dart
│   ├── persona_selection_page.dart
│   └── privacy_settings.dart
├── services/              # Business logic and external services
│   ├── chat_title_generator.dart
│   ├── email_verification_interface.dart
│   ├── email_verification_service.dart
│   ├── firestore.dart     # Database operations
│   ├── remote_config_service.dart # AI model configuration
│   ├── system_prompt_service.dart # AI prompt management
│   ├── theme_service.dart # Theme management
│   └── user_profile_updater.dart
├── theme/                 # Design system and theming
│   ├── app_colors.dart    # Color palette
│   ├── app_dimensions.dart # Spacing and sizing
│   ├── app_icons.dart     # Icon definitions
│   ├── app_theme.dart     # Theme configuration
│   ├── app_typography.dart # Text styles
│   ├── theme.dart         # Main theme export
│   └── theme_extensions.dart # Theme utilities
├── utils/                 # Utility classes and helpers
│   ├── admin/             # Admin tools and seeders
│   ├── gemini_stream_manager.dart
│   └── system_prompt_builder.dart
├── widgets/               # Reusable UI components
│   ├── composer_action_bar.dart
│   ├── email_verification_dialog.dart
│   ├── guided_path_card.dart
│   └── path_step_card.dart
├── scripts/               # Build and deployment scripts
├── firebase_options.dart  # Firebase configuration
├── hive_chat_controller.dart # Local chat storage
├── create_message.dart    # Message creation utilities
└── main.dart             # Application entry point
```

## Data Models

### Core User Models

#### User

Primary user entity with authentication and profile information:

- **id**: Unique user identifier (Firebase UID)
- **name**: Display name
- **email**: User email address
- **isOnboarded**: Onboarding completion status
- **isAdmin**: Administrative privileges flag
- **description**: Optional user description
- **preferredPersonaIds**: List of preferred AI coaching personalities
- **createdAt**: Account creation timestamp

#### UserProfile

Comprehensive user profile for AI personalization:

- **userId**: Reference to User entity
- **name, age, gender**: Basic demographics
- **familyStatus**: Relationship status
- **family**: List of family relationships
- **location**: Geographic information
- **facts**: Personal facts and details
- **likes/dislikes**: Preferences and aversions
- **preferences**: Structured preference data
- **goals**: Personal development objectives
- **personalityTraits**: Inferred personality characteristics
- **interactionHistory**: AI interaction metadata

### Chat & Communication Models

#### Chat

Chat session entity:

- **id**: Unique chat identifier
- **title**: Chat title (auto-generated or custom)
- **ownerId**: User who owns the chat
- **systemPersonaId**: Selected AI personality
- **startedDate/lastUpdatedDate**: Timestamps
- **isCompleted**: Completion status
- **archived**: Archive status
- **metadata**: Additional chat data

#### Message

Individual chat messages:

- **id**: Unique message identifier
- **chatId**: Parent chat reference
- **chatOwnerId**: Chat owner for security rules
- **userId**: Message author (user messages)
- **systemPersonaId**: AI persona (system messages)
- **postedDate**: Message timestamp
- **type**: Message type (text, image, text_image)
- **textContent**: Message text
- **imageUrl**: Image attachment URL
- **reactionCounts**: Like/dislike counts
- **edited/editDate**: Edit status and timestamp
- **replyToMessageId**: Reply thread reference
- **metadata**: Additional message data

#### SystemPersona

AI coaching personalities:

- **id**: Unique persona identifier
- **name**: Persona name (e.g., "The Motivator")
- **description**: Personality description and specialties
- **avatarUrl**: Profile image URL
- **isActive**: Availability status
- **metadata**: Coaching style and approach data

### Guided Paths Models

#### GuidedPath

Structured personal development journeys:

- **id**: Unique path identifier
- **name**: Path name (e.g., "Zero-to-Flow")
- **category**: Path category (Focus & Productivity, Mindset & Resilience, Habit Formation, Life Design)
- **description**: Path overview and objectives
- **stepCount**: Number of steps in the path
- **targetUserTier**: Access level (free, paid)
- **imageUrl**: Path cover image
- **estimatedCompletionTimeMinutes**: Expected duration
- **difficultyLevel**: Difficulty rating (beginner, intermediate, advanced)
- **prerequisites**: Required prior paths or skills
- **isActive**: Availability status
- **createdAt/updatedAt**: Timestamps
- **metadata**: Additional path configuration

#### PathStep

Individual steps within guided paths:

- **id**: Unique step identifier
- **guidedPathId**: Parent path reference
- **stepNumber**: Sequential step number
- **title**: Step title
- **description**: Step instructions and content
- **type**: Step type (reading, exercise, reflection, etc.)
- **estimatedCompletionTimeMinutes**: Expected duration
- **content**: Rich content data (text, images, exercises)
- **isActive**: Availability status
- **createdAt/updatedAt**: Timestamps
- **metadata**: Step-specific configuration

#### UserPathProgress

User progress tracking for guided paths:

- **id**: Unique progress identifier
- **userId**: User reference
- **guidedPathId**: Path reference
- **currentStepNumber**: Current step position
- **completedSteps**: List of completed step numbers
- **isCompleted**: Path completion status
- **startedAt**: Path start timestamp
- **lastAccessedAt**: Last activity timestamp
- **completedAt**: Completion timestamp
- **metadata**: Progress-specific data

## Services

### FirestoreService

Central database service handling all Firestore operations:

**User Management:**

- `getUser(userId)`: Retrieve user by ID
- `createUser(user)`: Create new user
- `updateUserOnboarding()`: Complete user onboarding
- `updateUserProfile()`: Update user profile data

**Chat Operations:**

- `createChat()`: Create new chat session
- `getChats(ownerId)`: Get user's chats
- `getChat(chatId, ownerId)`: Get specific chat
- `addMessage()`: Add message to chat
- `getMessages()`: Retrieve chat messages
- `updateChatTitle()`: Update chat title

**SystemPersona Management:**

- `getSystemPersonas()`: Get all active personas
- `getSystemPersona(id)`: Get specific persona
- `createSystemPersonas()`: Bulk create personas

**Guided Paths:**

- `getAccessibleGuidedPaths(userId)`: Get paths based on user tier
- `getGuidedPath(id)`: Get specific path
- `getPathSteps(pathId)`: Get path steps
- `getUserPathProgress()`: Get user progress
- `updateUserPathProgress()`: Update progress tracking

### ChatTitleGenerator

AI-powered chat title generation:

- Uses Gemini 2.5 Flash model
- Generates concise, relevant titles from chat content
- Handles empty or "New Chat" titles automatically

### UserProfileUpdater

AI-driven user profile enhancement:

- Analyzes chat conversations for insights
- Updates UserProfile with inferred information
- Maintains user privacy and consent
- Uses structured prompts for consistent data extraction

### EmailVerificationService

Email verification workflow management:

- Handles email verification status checking
- Manages verification email sending
- Provides user-friendly verification interface

### ThemeService

Theme and appearance management:

- Handles light/dark theme switching
- Persists theme preferences
- Provides theme-related utilities

### RemoteConfigService

Dynamic AI model configuration through Firebase Remote Config:

- Subscription-based model selection (free vs premium tiers)
- Use case optimization (chat, title generation, profile updates)
- Real-time configuration updates without app restarts
- Graceful fallback to default models when remote config fails
- Integration with SubscriptionService for automatic tier detection

### SystemPromptService

Dynamic system prompt management through Firebase Remote Config and local assets:

- Template-based prompt system with placeholder injection
- Remote configuration for real-time prompt updates
- Local asset fallback for reliable prompt delivery
- Subscription-tier based prompt selection
- Caching system for optimal performance

## Pages/Screens

### Core Navigation

- **MainNavigationPage**: Bottom navigation with dynamic admin tab
- **HomePage**: Dashboard with guided paths and progress overview
- **ChatListPage**: Chat list and management interface
- **AccountPage**: User profile and settings management

### Authentication & Onboarding

- **LoginPage**: Firebase authentication with Google OAuth
- **EmailVerificationPage**: Email verification workflow
- **OnboardingPage**: User setup and persona selection

### Chat & AI Interaction

- **ChatPage**: Real-time AI chat interface with streaming responses
- **PersonaSelectionPage**: AI personality selection for new chats

### Guided Paths

- **GuidedPathDetailPage**: Path overview, progress, and step navigation
- **PathStepDetailPage**: Individual step content and completion

### Settings & Privacy

- **PrivacySettingsPage**: UserProfile data management and privacy controls

### Admin Tools

- **PersonaSeederPage**: SystemPersona and GuidedPath seeding interface

## Utilities

### Admin Tools (`lib/utils/admin/`)

- **SystemPersonaSeeder**: Seeds default AI coaching personalities from JSON data
- **GuidedPathSeeder**: Seeds default guided paths across all categories

### AI Integration (`lib/utils/`)

- **SystemPromptBuilder**: Constructs personalized system prompts for Gemini
- **GeminiStreamManager**: Manages streaming AI responses and chat state

## Theme System

The Upshift theme system embodies the "Growth Through Clarity" design philosophy with:

### Design Principles

- **Forward Momentum**: Visual elements that suggest progress and growth
- **Personal Empowerment**: Colors and typography that inspire confidence
- **Thoughtful Progression**: Spacing and layout that guide user attention
- **Accessibility**: WCAG-compliant colors and responsive design

### Key Components

- **AppColors**: Comprehensive color palette with semantic naming
- **AppTypography**: Google Fonts-based typography system
- **AppDimensions**: 8dp grid-based spacing and sizing
- **AppIcons**: Consistent iconography
- **ThemeExtensions**: Context-based theme utilities

### Category Colors

- **Focus & Productivity**: Blue tones for clarity and focus
- **Mindset & Resilience**: Green tones for growth and stability
- **Habit Formation**: Orange tones for energy and motivation
- **Life Design**: Purple tones for creativity and vision

For detailed theme documentation, see `lib/theme/README.md`.

## Development Setup

### Prerequisites

- Flutter SDK 3.8.1+ ([Installation Guide](https://flutter.dev/docs/get-started/install))
- Android Studio or VS Code with Flutter extensions
- Xcode (for iOS development, macOS only)
- Firebase CLI ([Installation Guide](https://firebase.google.com/docs/cli))
- Git

### Installation Steps

1. **Clone the repository:**

   ```bash
   git clone <repository-url>
   cd upshift
   ```

2. **Install dependencies:**

   ```bash
   flutter pub get
   ```

3. **Set up Firebase:**

   - Create a Firebase project at [Firebase Console](https://console.firebase.google.com/)
   - Enable Authentication, Firestore, and Cloud Functions
   - Configure Firebase for Flutter:
     ```bash
     firebase use --add
     flutterfire configure
     ```

4. **Configure environment variables:**

   - Copy `.env.example` to `.env`
   - Add required API keys and configuration

5. **Generate code:**

   ```bash
   flutter packages pub run build_runner build
   ```

6. **Run the application:**
   ```bash
   flutter run
   ```

## Useful Commands

### Development

```bash
# Install dependencies
flutter pub get

# Run on specific platform
flutter run -d android
flutter run -d ios
flutter run -d web

# Hot reload (during development)
r  # Hot reload
R  # Hot restart
q  # Quit

# Code generation
flutter packages pub run build_runner build
flutter packages pub run build_runner watch  # Watch mode

# Clean and rebuild
flutter clean
flutter pub get
flutter packages pub run build_runner build --delete-conflicting-outputs
```

### Testing & Quality

```bash
# Run tests
flutter test
flutter test --coverage

# Integration tests
flutter test integration_test/

# Code analysis
flutter analyze
dart fix --dry-run
dart fix --apply

# Format code
dart format .
```

### Building & Deployment

```bash
# Build APK (Android)
flutter build apk --release
flutter build apk --debug

# Build App Bundle (Android)
flutter build appbundle --release

# Build iOS
flutter build ios --release

# iOS app bundle for App Store - https://docs.flutter.dev/deployment/ios 
flutter build ipa --release

# Build for web
flutter build web --release
```

### JSON Schema Validation

The project includes JSON schema files for validating data structure consistency. For comprehensive validation instructions, see the dedicated documentation:

📋 **[Data Validation Guide](data/DATA_VALIDATION.md)**

This guide covers:

- SystemPersona data validation
- Guided Paths data validation
- UserProfile data validation
- Error handling and troubleshooting
- CI/CD integration examples

## Firebase Configuration

### Firestore Collections

The application uses the following Firestore collections:

```
/users/{userId}
├── id: string (Firebase UID)
├── name: string
├── email: string
├── isOnboarded: boolean
├── isAdmin: boolean
├── description?: string
├── preferredPersonaIds: string[]
└── createdAt: timestamp

/userProfiles/{userId}
├── userId: string
├── name?: string
├── age?: number
├── gender?: string
├── familyStatus?: string
├── family?: RelationInfo[]
├── location?: Location
├── facts?: Fact[]
├── likes?: string[]
├── dislikes?: string[]
├── preferences?: object
├── goals?: Goal[]
├── personalityTraits?: string[]
└── interactionHistory: InteractionHistory

/chats/{chatId}
├── id: string
├── title: string
├── ownerId: string
├── systemPersonaId: string
├── startedDate: timestamp
├── lastUpdatedDate: timestamp
├── isCompleted: boolean
├── archived?: boolean
└── metadata?: object

/chats/{chatId}/messages/{messageId}
├── id: string
├── chatId: string
├── chatOwnerId: string
├── userId?: string
├── systemPersonaId?: string
├── postedDate: timestamp
├── isDeleted: boolean
├── type: string
├── textContent?: string
├── imageUrl?: string
├── reactionCounts: object
├── edited?: boolean
├── editDate?: timestamp
├── replyToMessageId?: string
└── metadata?: object

/systemPersonas/{personaId}
├── id: string
├── name: string
├── description: string
├── avatarUrl: string
├── isActive: boolean
└── metadata?: object

/guidedPaths/{pathId}
├── id: string
├── name: string
├── category: string
├── description: string
├── stepCount: number
├── targetUserTier: string
├── imageUrl?: string
├── estimatedCompletionTimeMinutes?: number
├── difficultyLevel?: string
├── prerequisites?: string[]
├── isActive: boolean
├── createdAt: timestamp
├── updatedAt?: timestamp
└── metadata?: object

/pathSteps/{stepId}
├── id: string
├── guidedPathId: string
├── stepNumber: number
├── title: string
├── description: string
├── type: string
├── estimatedCompletionTimeMinutes?: number
├── content?: object
├── isActive: boolean
├── createdAt: timestamp
├── updatedAt?: timestamp
└── metadata?: object

/userPathProgress/{progressId}
├── id: string
├── userId: string
├── guidedPathId: string
├── currentStepNumber: number
├── completedSteps: number[]
├── isCompleted: boolean
├── startedAt: timestamp
├── lastAccessedAt: timestamp
├── completedAt?: timestamp
└── metadata?: object
```

### Security Rules

Firestore security rules are defined in `firestore.rules`. Key principles:

- Users can only access their own data
- Chat messages include `chatOwnerId` for efficient security rules
- SystemPersonas and GuidedPaths are publicly readable
- Admin users have additional privileges for seeding data

### Firebase Setup Commands

```bash
# Initialize Firebase project
firebase init

# Deploy Firestore rules
firebase deploy --only firestore:rules

# Deploy Cloud Functions
firebase deploy --only functions

# Set Firebase project
firebase use --add
firebase use <project-id>
```

### Release Android App

Generate launcher icons with:

```bash
flutter pub run flutter_launcher_icons
```

```bash
# Build APK
flutter build apk --release

# Deploy to Google Play
# Follow standard Google Play deployment process
```

For more information, see:

- https://docs.flutter.dev/deployment/android
- Generate keystore: keytool -genkey -v -keystore $env:USERPROFILE\upload-keystore.jks -storetype JKS -keyalg RSA -keysize 2048 -validity 10000 -alias upload
- Or use generated keystore from C:\Users\<USER>\upload-keystore.jks

### Release iOS App

TODO

## AI Integration

### Gemini AI Configuration

Upshift uses Google's Gemini 2.5 Flash model through Firebase AI for intelligent conversations:

```dart
// Model initialization
final model = FirebaseAI.googleAI().generativeModel(
  model: 'gemini-2.5-flash',
  safetySettings: [
    SafetySetting(HarmCategory.dangerousContent, HarmBlockThreshold.none),
  ],
  generationConfig: GenerationConfig(temperature: 0.3),
);
```

### System Prompt Integration

The prompt management system combines `SystemPromptService` and `SystemPromptBuilder` for dynamic, personalized AI context:

**SystemPromptService Features:**

1. **Template Management**: Markdown templates with placeholder injection
2. **Remote Configuration**: Dynamic prompt updates via Firebase Remote Config
3. **Local Asset Fallback**: Reliable delivery when remote config fails
4. **Subscription Awareness**: Different prompts for free vs premium users

**SystemPromptBuilder Integration:**

1. **Base Instructions**: Core coaching principles and behavior guidelines
2. **UserProfile Integration**: Personal context including demographics, goals, and preferences
3. **Conversation Context**: Chat history and current objectives
4. **Persona-Specific Instructions**: Tailored to selected AI personality
5. **Template Injection**: Configurable prompts with entity-level placeholders

### Key Features

- **Streaming Responses**: Real-time message generation with `GeminiStreamManager`
- **Context Awareness**: Persistent memory through UserProfile integration
- **Safety Controls**: Configurable content filtering and safety settings
- **Error Handling**: Graceful fallbacks for API failures
- **Rate Limiting**: Built-in request throttling and retry logic

### AI Services

- **ChatTitleGenerator**: Auto-generates meaningful chat titles using configurable prompts
- **UserProfileUpdater**: Extracts insights from conversations to enhance user profiles
- **SystemPromptBuilder**: Constructs context-aware prompts for personalized interactions
- **SystemPromptService**: Manages dynamic prompt templates with remote configuration

## Remote Configuration

### RemoteConfigService

The `RemoteConfigService` provides dynamic AI model configuration through Firebase Remote Config, enabling real-time updates to AI model selection without app updates.

#### Key Features

- **Subscription-based Model Selection**: Different AI models for free vs premium users
- **Use Case Optimization**: Specialized models for different AI operations
- **Fallback System**: Graceful degradation when remote config is unavailable
- **Real-time Updates**: Configuration changes take effect without app restarts

#### Public Methods

**Model Selection Methods:**

```dart
// Get model for chat conversations
String getChatModel({UserSubscription? userSubscription})
String getChatModelForCurrentUser()

// Get model for title generation
String getTitleGenerationModel({UserSubscription? userSubscription})
String getTitleGenerationModelForCurrentUser()

// Get model for profile updates
String getProfileUpdateModel({UserSubscription? userSubscription})
String getProfileUpdateModelForCurrentUser()
```

**Configuration Management:**

```dart
// Initialize the service (called in main.dart)
Future<void> initialize()

// Manually refresh configuration
Future<void> refresh()

// Get all config values for debugging
Map<String, String> getAllConfigValues()
```

#### Remote Config Keys

The service uses the following Firebase Remote Config keys:

**Free Tier Models:**

- `gemini_chat_model` - Model for free user chat conversations
- `gemini_title_generation_model` - Model for free user title generation
- `gemini_profile_update_model` - Model for free user profile updates

**Premium Tier Models:**

- `gemini_premium_chat_model` - Model for premium user chat conversations
- `gemini_premium_title_generation_model` - Model for premium user title generation
- `gemini_premium_profile_update_model` - Model for premium user profile updates

#### Default Values

All models default to `'gemini-2.5-flash'` when remote configuration is unavailable:

```dart
// Default fallback models
static const String _defaultModel = 'gemini-2.5-flash';
static const String _defaultPremiumModel = 'gemini-2.5-flash';
```

#### Subscription Integration

The service automatically integrates with the `SubscriptionService` to determine user tier:

```dart
// Automatic subscription detection
final model = RemoteConfigService.instance.getChatModelForCurrentUser();

// Manual subscription specification
final premiumSubscription = UserSubscription(
  isActive: true,
  entitlements: [SubscriptionEntitlement.premium],
  lastChecked: DateTime.now(),
);
final model = RemoteConfigService.instance.getChatModel(
  userSubscription: premiumSubscription,
);
```

#### Usage Examples

**In Chat Services:**

```dart
// ChatPage - Dynamic model selection
final modelName = RemoteConfigService.instance.getChatModelForCurrentUser();
final model = FirebaseAI.googleAI().generativeModel(
  model: modelName,
  generationConfig: GenerationConfig(temperature: 0.7),
);
```

**In Title Generation:**

```dart
// ChatTitleGenerator - Configurable model
final modelName = RemoteConfigService.instance.getTitleGenerationModelForCurrentUser();
final model = FirebaseAI.googleAI().generativeModel(model: modelName);
```

**In Profile Updates:**

```dart
// UserProfileUpdater - Subscription-aware model selection
final modelName = RemoteConfigService.instance.getProfileUpdateModelForCurrentUser();
final model = FirebaseAI.googleAI().generativeModel(model: modelName);
```

#### Error Handling

The service implements comprehensive error handling:

- **Initialization Failures**: App continues with default models
- **Network Issues**: Cached/default values are used
- **Invalid Configurations**: Automatic fallback to defaults
- **Subscription Service Errors**: Treats as free tier

#### Configuration Updates

To update AI models remotely:

1. **Access Firebase Console** → Remote Config
2. **Update the desired keys** (e.g., `gemini_premium_chat_model`)
3. **Publish changes** - Updates take effect within the configured fetch interval
4. **Monitor logs** for configuration change confirmations

#### Testing

The service includes comprehensive unit tests covering:

- Singleton pattern verification
- Model selection logic for different subscription tiers
- Error handling and fallback scenarios
- Default value validation
- API consistency checks

Run tests with:

```bash
flutter test test/services/remote_config_service_test.dart
```

## System Prompt Management

### SystemPromptService

The `SystemPromptService` provides dynamic system prompt management through Firebase Remote Config and local asset templates, enabling real-time updates to AI prompts without app updates.

#### Key Features

- **Template-based System**: Markdown templates with placeholder injection
- **Remote Configuration**: Dynamic prompt updates via Firebase Remote Config
- **Local Asset Fallback**: Reliable prompt delivery when remote config fails
- **Subscription-aware**: Different prompts for free vs premium users
- **Caching System**: Optimized performance with intelligent caching
- **Placeholder Injection**: Entity-level parameter substitution

#### Public Methods

**Prompt Template Methods:**

```dart
// Get prompt for chat with persona
Future<String> getChatWithPersonaPrompt({UserSubscription? userSubscription})

// Get prompt for title generation
Future<String> getTitleGenerationPrompt({UserSubscription? userSubscription})

// Get prompt for profile updates
Future<String> getProfileUpdatePrompt({UserSubscription? userSubscription})
```

**Service Management:**

```dart
// Initialize the service (called in main.dart)
Future<void> initialize()

// Clear cached templates
void clearCache()

// Get all cached prompts for debugging
Map<String, String> getCachedPrompts()
```

#### Template System

**Template Location**: `assets/prompts/`

- `chat_with_persona.md` - Main chat conversation prompt template
- `title_generation.md` - Chat title generation prompt template
- `profile_update.md` - User profile update prompt template

**Placeholder Syntax:**

Templates use high-level entity placeholders:

```markdown
# AI Coach Role & Identity

{systemPersona}

# Current Context

{conversationContext}

# User Profile

{userProfile}

# Response Guidelines

...
```

#### Remote Config Keys

The service uses Firebase Remote Config keys following the same tier pattern:

**Free Tier Prompts:**

- `system_prompt_chat_with_persona` - Chat conversation prompt for free users
- `system_prompt_title_generation` - Title generation prompt for free users
- `system_prompt_profile_update` - Profile update prompt for free users

**Premium Tier Prompts:**

- `system_prompt_premium_chat_with_persona` - Chat conversation prompt for premium users
- `system_prompt_premium_title_generation` - Title generation prompt for premium users
- `system_prompt_premium_profile_update` - Profile update prompt for premium users

#### Integration with SystemPromptBuilder

The `SystemPromptBuilder` has been enhanced with a new method that uses `SystemPromptService`:

```dart
// New configurable method
static Future<String> buildChatWithPersonaSystemPrompt({
  SystemPersona? systemPersona,
  UserProfile? userProfile,
  String? conversationContext,
  UserSubscription? userSubscription,
}) async {
  // Fetches template from SystemPromptService
  // Injects parameters into placeholders
  // Returns complete system prompt
}
```

#### Usage Examples

**In Chat Services:**

```dart
// Get configurable prompt template
final prompt = await SystemPromptBuilder.buildChatWithPersonaSystemPrompt(
  systemPersona: selectedPersona,
  userProfile: currentUserProfile,
  conversationContext: "New coaching session",
  userSubscription: userSubscription,
);

// Use with AI model
final model = FirebaseAI.googleAI().generativeModel(
  model: modelName,
  systemInstruction: Content.system(prompt),
);
```

**Adding New Prompt Templates:**

1. **Create template file** in `assets/prompts/new_template.md`
2. **Add remote config keys** to `RemoteConfigService`
3. **Add method** to `SystemPromptService` following existing patterns
4. **Update default values** in `_getDefaultValues()` method

#### Error Handling

The service implements comprehensive error handling:

- **Asset Loading Failures**: Automatic fallback to hardcoded default prompts
- **Remote Config Issues**: Graceful degradation to local asset templates
- **Template Parsing Errors**: Fallback to original `SystemPromptBuilder` methods
- **Subscription Service Errors**: Treats as free tier

#### Template Development

**Best Practices:**

- Use high-level placeholders (`{userProfile}` not `{userProfile.age}`)
- Maintain coaching philosophy and behavioral instructions
- Include clear response guidelines
- Test with both free and premium configurations
- Validate Markdown formatting

**Placeholder Guidelines:**

- `{systemPersona}` - Complete persona information and instructions
- `{userProfile}` - Complete user profile data formatted for AI context
- `{conversationContext}` - Current session context and timing information

#### Configuration Updates

To update system prompts remotely:

1. **Access Firebase Console** → Remote Config
2. **Update prompt keys** (e.g., `system_prompt_chat_with_persona`)
3. **Use Markdown format** with proper placeholder syntax
4. **Publish changes** - Updates take effect within fetch interval
5. **Monitor logs** for template loading confirmations

#### Testing

The service includes comprehensive unit tests covering:

- Template loading and caching mechanisms
- Remote config integration and fallback scenarios
- Subscription tier logic and prompt selection
- Error handling and graceful degradation
- API consistency and placeholder injection

Run tests with:

```bash
flutter test test/services/system_prompt_service_test.dart
```

## Admin Features

### Seeding Tools

The application includes comprehensive seeding tools for initial data setup:

#### SystemPersona Seeding

```dart
// Seed all default personas from JSON
final personaIds = await SystemPersonaSeeder.seedDefaultPersonas();

// Check if personas already exist
final exist = await SystemPersonaSeeder.personasExist();

// Validate persona data
final isValid = await SystemPersonaSeeder.validatePersonaData();

// Get persona summary
final summary = await SystemPersonaSeeder.getPersonaSummary();
```

**Default Personas (loaded from `data/system_personas.json`):**

1. **Sage** - Seeker of Truth (contemplative, reflective approach)
2. **Mister Iron** - The Tough Love Coach (authoritative, directive approach)
3. **Zen Master** - The Calm Guide (mindful, gentle approach)
4. **Brorat** - The Funny Provocateur (provocative, humorous approach)
5. **Professor Plan** - The Analyst-Strategist (analytical, methodical approach)
6. **Sunny** - The Empath (encouraging, uplifting approach)
7. **Ms. Visionary** - The Inspirational Innovator (creative, imaginative approach)

#### GuidedPath Seeding

```dart
// Seed all default guided paths
final pathData = await GuidedPathSeeder.seedDefaultGuidedPaths();
```

**Default Paths by Category:**

**Focus & Productivity:**

- Zero-to-Flow (Free tier)
- Deep Work Mastery (Paid tier)
- Time Sovereignty (Paid tier)

**Mindset & Resilience:**

- Unshakeable Confidence (Free tier)
- Stress Alchemy (Paid tier)
- Growth Mindset Activation (Paid tier)

**Habit Formation:**

- Habit Architecture (Free tier)
- Breaking Chains (Paid tier)
- Consistency Engine (Paid tier)

**Life Design:**

- Purpose Discovery (Free tier)
- Values Alignment (Paid tier)
- Life Vision Creation (Paid tier)

### Admin Interface

The `PersonaSeederPage` provides a user-friendly interface for:

- Checking existing data status
- Seeding SystemPersonas with confirmation dialogs
- Seeding GuidedPaths with progress indicators
- Viewing seeding results and error handling
- Bulk operations with safety confirmations

### Access Control

Admin features are protected by:

- User authentication verification
- `isAdmin` flag in User model
- Dynamic UI rendering based on permissions
- Firestore security rules for admin operations

## Contributing

### Code Style

- Follow Dart/Flutter conventions
- Use `dart format` for consistent formatting
- Run `flutter analyze` before committing
- Write meaningful commit messages

### Testing

- Write unit tests for business logic
- Add widget tests for UI components
- Include integration tests for critical flows
- Maintain test coverage above 80%

### Documentation

- Update README.md for architectural changes
- Document new models in the Data Models section
- Add inline documentation for complex functions
- Update API documentation for service changes

## License

This project is proprietary software. All rights reserved.
