# Dynamic Topic Guidance Demo

This document demonstrates the new dynamic topic-based guidance feature implemented in the Upshift AI coaching system.

## Overview

The system now provides context-specific guidance to the AI coach based on whether the chat is:
1. **General chat** - Open coaching conversation
2. **PathStep-initiated chat** - Focused on a specific guided path step

## Implementation Details

### 1. Updated Chat Prompt Template
- Added `{discussionTopic}` placeholder in `assets/prompts/chat_with_persona.md`
- This section provides dynamic guidance to the AI coach

### 2. Enhanced SystemPromptBuilder
- Added `pathStep` parameter to `buildChatWithPersonaSystemPrompt()`
- Implemented `buildDiscussionTopicSection()` method for topic generation
- Integrated topic injection into prompt building process

### 3. Updated ChatPage
- Modified to pass `pathStep` context to SystemPromptBuilder
- Enables topic-aware prompt generation

## Example Topic Guidance

### General Chat (No PathStep)
```markdown
## Session Focus: Personal Check-In & Growth Exploration

**Context:** This is an open coaching conversation where the user can explore any aspect of their personal growth journey.

**Coaching Instructions:**
- Start with open-ended questions to understand what's on their mind today
- Ask questions like "What's on your mind today?" or "How can I help you today?" or "What would you like to explore in our conversation?"
- Listen actively and follow their lead while providing supportive guidance
- Help them identify areas for growth, reflection, or action
- Encourage deeper self-reflection through thoughtful questions
- Provide practical insights and actionable next steps when appropriate
```

### PathStep-Initiated Chat
```markdown
## Session Focus: Identify Your Peak Performance Hours

**Context:** Discover when you naturally have the most energy and focus throughout the day to optimize your productivity schedule.

**Key Reflection Areas:**
- When do you feel most alert and focused during the day?
- What activities drain your energy the most?
- How does your current schedule align with your natural energy patterns?
- What would an ideal day look like if you could design it around your energy levels?

**Session Goal:** Create a personalized daily energy map showing your high, medium, and low energy periods with specific time blocks.

**Coaching Instructions:**
- Focus the conversation on helping the user work through this specific step
- Guide them through the reflection prompts naturally during conversation
- Help them understand and apply the concepts to their personal situation
- When the user has adequately covered the reflection areas and understands the concepts, acknowledge their progress and thank them for engaging with this step
- Encourage them to continue their growth journey
```

## Benefits

1. **Structured Conversations**: AI coaches receive clear guidance on session focus
2. **Adaptive Behavior**: Different approaches for general vs. specific coaching sessions
3. **Goal-Oriented**: PathStep chats maintain focus on specific learning objectives
4. **Natural Flow**: Reflection prompts are integrated naturally into conversation
5. **Session Completion**: AI recognizes when objectives are met and provides closure

## Testing

- All existing tests pass
- New tests added for PathStep and general chat scenarios
- Code analysis shows no compilation errors
- Implementation is backward compatible
