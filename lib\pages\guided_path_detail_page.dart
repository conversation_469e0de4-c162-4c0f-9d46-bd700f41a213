import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/models.dart' as models;
import '../services/firestore.dart';
import '../services/in_app_rating_service.dart';
import '../widgets/path_step_card.dart';
import 'path_step_detail_page.dart';

class GuidedPathDetailPage extends StatefulWidget {
  final String pathId;

  const GuidedPathDetailPage({super.key, required this.pathId});

  @override
  State<GuidedPathDetailPage> createState() => _GuidedPathDetailPageState();
}

class _GuidedPathDetailPageState extends State<GuidedPathDetailPage> {
  models.GuidedPath? _guidedPath;
  List<models.PathStep> _pathSteps = [];
  models.UserPathProgress? _userProgress;
  models.User? _currentUser;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadPathData();
  }

  Future<void> _loadPathData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        setState(() {
          _errorMessage = 'User not authenticated';
          _isLoading = false;
        });
        return;
      }

      // Load path, steps, user progress, and current user in parallel
      final results = await Future.wait([
        FirestoreService.getGuidedPath(widget.pathId),
        FirestoreService.getPathSteps(widget.pathId),
        FirestoreService.getUserPathProgress(currentUser.uid, widget.pathId),
        FirestoreService.getUser(currentUser.uid),
      ]);

      final guidedPath = results[0] as models.GuidedPath?;
      final pathSteps = results[1] as List<models.PathStep>;
      var userProgress = results[2] as models.UserPathProgress?;
      final user = results[3] as models.User?;

      // Auto-create UserPathProgress if it doesn't exist
      if (userProgress == null && guidedPath != null) {
        final now = DateTime.now();
        userProgress = models.UserPathProgress(
          userId: currentUser.uid,
          pathId: widget.pathId,
          isCompleted: false,
          startedDate: now,
          lastAccessedDate: now,
        );
        await FirestoreService.createOrUpdateUserPathProgress(userProgress);
      }

      if (guidedPath == null) {
        setState(() {
          _errorMessage = 'Guided path not found';
          _isLoading = false;
        });
        return;
      }

      setState(() {
        _guidedPath = guidedPath;
        _pathSteps = pathSteps;
        _userProgress = userProgress;
        _currentUser = user;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load path data: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _startPath() async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      await FirestoreService.startUserPath(currentUser.uid, widget.pathId);
      await _loadPathData(); // Refresh data

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Path started! Good luck on your journey.'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to start path: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _completeStep(models.PathStep pathStep) async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      await FirestoreService.completeUserPathStep(
        currentUser.uid,
        widget.pathId,
        pathStep.id!,
      );
      await _loadPathData(); // Refresh data

      // Request in-app rev iew after successful step completion
      // This runs in the background and won't block the UI
      InAppRatingService.instance.requestReviewIfNeeded().catchError((error) {
        // Log error but don't show to user - review prompts should be silent
        debugPrint('Failed to request in-app review: $error');
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Step ${pathStep.stepNumber} completed!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to complete step: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Determines if a step should be locked based on user progress and admin status
  bool _isStepLocked(models.PathStep step) {
    // Admin users can access any step
    if (_currentUser?.isAdmin == true) {
      return false;
    }

    // If no progress exists, only the first step should be accessible
    if (_userProgress == null) {
      return step.stepNumber != 1;
    }

    // If step is already completed, it's not locked
    if (_isStepCompleted(step)) {
      return false;
    }

    // If it's the first step, it's never locked
    if (step.stepNumber == 1) {
      return false;
    }

    // Check if previous step is completed
    final previousStep = _pathSteps.firstWhere(
      (s) => s.stepNumber == step.stepNumber - 1,
      orElse: () => step, // If no previous step found, don't lock
    );

    return !_isStepCompleted(previousStep);
  }

  /// Check if a specific step is completed
  bool _isStepCompleted(models.PathStep step) {
    if (_userProgress == null || step.id == null) return false;
    final stepProgress = _userProgress!.getStepProgress(step.id!);
    return stepProgress?.isCompleted ?? false;
  }

  /// Check if a specific step is the current step (next to be worked on)
  bool _isCurrentStep(models.PathStep step) {
    if (_userProgress == null || step.id == null) return false;

    // If this step is already completed, it's not current
    if (_isStepCompleted(step)) return false;

    // If it's the first step and not completed, it's current
    if (step.stepNumber == 1) return true;

    // Check if the previous step is completed
    final previousStep = _pathSteps.firstWhere(
      (s) => s.stepNumber == step.stepNumber - 1,
      orElse: () => step,
    );

    return _isStepCompleted(previousStep);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_guidedPath?.name ?? 'Guided Path'),
        actions: [
          if (_guidedPath != null)
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _loadPathData,
            ),
        ],
      ),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadPathData,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_guidedPath == null) {
      return const Center(child: Text('Path not found'));
    }

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Path header
          _buildPathHeader(),

          // Progress section
          if (_userProgress != null) _buildProgressSection(),

          // Steps section
          _buildStepsSection(),

          const SizedBox(height: 100), // Space for bottom bar
        ],
      ),
    );
  }

  Widget _buildPathHeader() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Category badge
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _getCategoryColor(),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              _guidedPath!.category,
              style: theme.textTheme.labelMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),

          const SizedBox(height: 12),

          // Title
          Text(
            _guidedPath!.name,
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 8),

          // Description
          Text(
            _guidedPath!.description,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressSection() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final progress = _userProgress!.getProgressPercentage(
      _guidedPath!.stepCount,
    );

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Your Progress',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '${(progress * 100).round()}%',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: colorScheme.primary.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
          ),
          const SizedBox(height: 8),
          Text(
            '${_userProgress!.getCompletedStepsCount()} of ${_guidedPath!.stepCount} steps completed',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            'Steps',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
        ),
        ..._pathSteps.map(
          (step) => PathStepCard(
            pathStep: step,
            userProgress: _userProgress,
            isCurrentStep: _isCurrentStep(step),
            isCompleted: _isStepCompleted(step),
            isLocked: _isStepLocked(step),
            onTap: () => _navigateToStepDetail(step),
            onComplete: () => _completeStep(step),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomBar() {
    if (_guidedPath == null) return const SizedBox.shrink();

    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(child: _buildActionButton()),
    );
  }

  Widget _buildActionButton() {
    if (_userProgress == null) {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: _startPath,
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
          child: const Text('Start Path'),
        ),
      );
    }

    if (_userProgress!.isCompleted) {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: null,
          icon: const Icon(Icons.check_circle),
          label: const Text('Path Completed'),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
          ),
        ),
      );
    }

    final currentStep = _pathSteps.firstWhere(
      (step) => _isCurrentStep(step),
      orElse: () => _pathSteps.first,
    );

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () => _navigateToStepDetail(currentStep),
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
        ),
        child: Text(
          'Continue',
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }

  void _navigateToStepDetail(models.PathStep step) {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => PathStepDetailPage(
              pathStep: step,
              guidedPath: _guidedPath!,
              userProgress: _userProgress,
            ),
          ),
        )
        .then((_) => _loadPathData()); // Refresh when returning
  }

  Color _getCategoryColor() {
    switch (_guidedPath!.category) {
      case 'Focus & Productivity':
        return Colors.blue;
      case 'Mindset & Resilience':
        return Colors.green;
      case 'Habit Formation':
        return Colors.orange;
      case 'Life Design':
        return Colors.purple;
      default:
        return Theme.of(context).colorScheme.primary;
    }
  }
}
