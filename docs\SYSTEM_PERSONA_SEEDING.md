# SystemPersona Seeding Guide

This guide explains how to seed the default SystemPersona entities into your Firestore database for the Upshift app.

## Overview

The SystemPersona entities represent different coaching styles that users can select during onboarding. The system includes 7 diverse personas loaded from JSON data:

- **Sage** - Seeker of Truth (contemplative, reflective approach)
- **Mister Iron** - The Tough Love Coach (authoritative, directive approach)
- **Zen Master** - The Calm Guide (mindful, gentle approach)
- **B<PERSON>rat** - The Funny Provocateur (provocative, humorous approach)
- **Professor Plan** - The Analyst-Strategist (analytical, methodical approach)
- **Sunny** - The Empath (encouraging, uplifting approach)
- **Ms. Visionary** - The Inspirational Innovator (creative, imaginative approach)

## Seeding Methods

### JSON-Based Seeding

The `SystemPersonaSeeder` class loads persona data from `data/system_personas.json`:

```dart
import 'package:upshift/utils/admin/system_persona_seeder.dart';

// Seed only if no personas exist (recommended)
final createdIds = await SystemPersonaSeeder.seedIfEmpty();
if (createdIds.isNotEmpty) {
  print('Created ${createdIds.length} personas');
} else {
  print('Personas already exist');
}
```

1. **The Motivator** - Uplifting and encouraging coach
2. **Brorat** - A playful and provocative coach who breaks excuses with humor and bold truths
3. **The Strategist** - Analytical and methodical coach
4. **The Dark Voice** - Brutally honest coach
5. **Supportive Friend** - Compassionate and nurturing coach
6. **The Innovator** - Creative and forward-thinking coach
7. **Mister Iron** - Fierce and determined coach
8. **The Philosopher** - Wise and contemplative coach
9. **The Pragmatist** - Practical and results-oriented coach
10. **The Catalyst** - Energetic and transformative coach

## Seeding Methods

### Method 1: In-App Admin Page (Recommended for Development)

Use the built-in admin page for a visual interface:

1. **Add the admin page to your app navigation** (temporary for seeding):

```dart
// In your main navigation or debug menu
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const PersonaSeederPage(),
  ),
);
```

2. **Import the page**:

```dart
import 'package:upshift/pages/admin/persona_seeder_page.dart';
```

3. **Use the interface**:
   - View current persona count and status
   - Click "Seed If Empty" to create personas only if none exist
   - Click "Force Seed" to create personas regardless (may create duplicates)
   - See real-time feedback and error handling

### Method 3: Programmatic Seeding

Use the utility class directly in your code:

```dart
import 'package:upshift/utils/admin/system_persona_seeder.dart';

// Seed only if no personas exist (recommended)
final createdIds = await SystemPersonaSeeder.seedIfEmpty();
if (createdIds.isNotEmpty) {
  print('Created ${createdIds.length} personas');
} else {
  print('Personas already exist');
}

// Force seed (creates duplicates)
final allIds = await SystemPersonaSeeder.seedDefaultPersonas();
print('Created ${allIds.length} personas');

// Check if personas exist
final exist = await SystemPersonaSeeder.personasExist();
print('Personas exist: $exist');

// Validate persona data
final isValid = await SystemPersonaSeeder.validatePersonaData();
print('Persona data is valid: $isValid');

// Get persona summary
final summary = await SystemPersonaSeeder.getPersonaSummary();
print('Total personas: ${summary['totalCount']}');
print('Active personas: ${summary['activeCount']}');

// Get persona count
final count = await SystemPersonaSeeder.getPersonaCount();
print('Total personas available: $count');
```

## Persona Data Structure

Each SystemPersona includes the following fields (loaded from JSON):

```dart
SystemPersona(
  name: 'Sage',                                    // Required: Persona name
  title: 'Seeker of Truth',                       // Required: Persona title/role
  description: 'A wise and contemplative coach...', // Required: Detailed description
  avatarUrl: 'assets/persona-images/ThePhilosopherPhoto.webp', // Required: Avatar image path
  isActive: true,                                  // Required: Active status
  coachingStyle: 'contemplative',                  // Required: Coaching style
  approach: 'reflective',                          // Required: General approach
  specialties: [                                   // Required: List of specialties
    'meaning-making',
    'self-discovery',
    'purpose-finding',
    'legacy',
    'balance'
  ],
  videoUrl: 'assets/persona-videos/ThePhilosopherVideo.mp4', // Required: Video path
  catchphrase: 'The right question can change everything.',   // Required: Memorable phrase
)
```

## Firebase Setup Requirements

### 1. Firestore Rules

Ensure your `firestore.rules` includes:

```javascript
// SystemPersona collection - read-only for authenticated users
match /systemPersonas/{personaId} {
  allow read: if request.auth != null;
  // Only allow writes from server-side or admin (no client writes)
  allow write: if false;
}
```

### 2. Firebase Project Configuration

Make sure your Firebase project is properly configured:

```bash
# Set the active Firebase project
firebase use --add

# Deploy Firestore rules
firebase deploy --only firestore:rules
```

### 3. Authentication

The seeding scripts require Firebase to be initialized. For production seeding, ensure you have:

- Valid Firebase service account credentials
- Proper project permissions
- Network access to Firestore

## Verification

After seeding, verify the personas were created successfully:

### Using Firestore Console

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Navigate to your project → Firestore Database
3. Check the `systemPersonas` collection
4. Verify 10 documents exist with `isActive: true`

### Using the App

1. Run the onboarding flow
2. Check that 10 personas are displayed for selection
3. Verify persona names and descriptions appear correctly

### Programmatically

```dart
final count = await FirestoreService.getSystemPersonaCount();
print('Total personas: $count'); // Should be 10

final activePersonas = await FirestoreService.getActiveSystemPersonas();
print('Active personas: ${activePersonas.length}'); // Should be 10
```

## Troubleshooting

### Common Issues

**"Personas already exist" message:**

- This is normal if you've already seeded the database
- Use the force option only if you need duplicates for testing

**Firebase initialization errors:**

- Check your `firebase_options.dart` configuration
- Verify your Firebase project is active: `firebase use --add`
- Ensure you have internet connectivity

**Permission denied errors:**

- Check your Firestore security rules
- Verify your authentication credentials
- For scripts, ensure you're running with proper permissions

**Import errors:**

- Run `flutter pub get` to ensure all dependencies are installed
- Check that all import paths are correct
- Verify the files exist in the expected locations

### Debug Mode

For additional debugging, you can modify the scripts to include more verbose logging or add breakpoints in the utility classes.

## Best Practices

1. **Always use "Seed If Empty"** for production to avoid duplicates
2. **Test seeding in development** before running in production
3. **Backup your Firestore data** before running seeding scripts
4. **Remove admin pages** from production builds
5. **Monitor Firestore usage** as seeding operations count toward your quota

## Integration with Onboarding

The seeded personas automatically integrate with the existing onboarding flow:

- `lib/pages/onboarding.dart` loads personas using `FirestoreService.getActiveSystemPersonas()`
- Users can select up to 3 preferred personas
- Selected persona IDs are stored in the user's `preferredPersonaIds` field
- The personas are used throughout the app for personalized coaching experiences
