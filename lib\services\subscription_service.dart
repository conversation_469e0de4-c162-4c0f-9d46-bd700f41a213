import 'dart:async';
import 'package:flutter/services.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:upshift/models/models.dart';
import 'package:upshift/services/logging_service.dart';
import 'package:upshift/services/analytics_service.dart';
import 'package:flutter/foundation.dart'
    show TargetPlatform, defaultTargetPlatform;

/// Service for managing RevenueCat subscriptions and entitlements
///
/// This service handles:
/// - RevenueCat SDK initialization and configuration
/// - Fetching available subscription packages/offers
/// - Making purchases and handling purchase flow
/// - Checking current entitlement status
/// - Restoring purchases
/// - Converting RevenueCat data to Upshift models
class SubscriptionService {
  static const String _premiumEntitlementId = 'premium';

  static const String rcAPIKeyAndroid = 'goog_GbzkabTtpEylqEGRVTFfBDZjVbl';
  static const String rcAPIKeyIOS = 'appl_gZHPasZprwvBUbJvFvcgyEeZqKg';

  static SubscriptionService? _instance;

  bool _isInitialized = false;
  final StreamController<UserSubscription> _subscriptionController =
      StreamController<UserSubscription>.broadcast();
  UserSubscription _currentSubscription = UserSubscription.free();

  /// Private constructor for singleton pattern
  SubscriptionService._();

  /// Get the singleton instance
  static SubscriptionService get instance {
    _instance ??= SubscriptionService._();
    return _instance!;
  }

  /// Initialize the RevenueCat SDK
  /// Must be called before using the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      LoggingService.instance.logInfo(
        'SubscriptionService',
        'Initializing RevenueCat SDK',
      );

      // Configure RevenueCat
      await Purchases.setLogLevel(LogLevel.debug);

      PurchasesConfiguration configuration;
      if (defaultTargetPlatform == TargetPlatform.iOS) {
        configuration = PurchasesConfiguration(rcAPIKeyIOS);
      } else {
        // defaultTargetPlatform == TargetPlatform.android)
        configuration = PurchasesConfiguration(rcAPIKeyAndroid);
      }

      await Purchases.configure(configuration);

      // Set up listener for purchase updates
      Purchases.addCustomerInfoUpdateListener(_onCustomerInfoUpdate);

      // Get initial customer info
      await _refreshCustomerInfo();

      _isInitialized = true;

      LoggingService.instance.logInfo(
        'SubscriptionService',
        'RevenueCat SDK initialized successfully',
      );
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        'SubscriptionService',
        'Failed to initialize RevenueCat SDK',
      );
      rethrow;
    }
  }

  /// Check if the service is initialized
  bool get isInitialized => _isInitialized;

  /// Get the current subscription status
  UserSubscription get currentSubscription => _currentSubscription;

  /// Stream of subscription changes
  Stream<UserSubscription> get subscriptionStream =>
      _subscriptionController.stream;

  /// Get available subscription packages
  Future<List<Package>> getAvailablePackages() async {
    _ensureInitialized();

    try {
      LoggingService.instance.logInfo(
        'SubscriptionService',
        'Fetching available packages',
      );

      final offerings = await Purchases.getOfferings();
      final currentOffering = offerings.current;

      if (currentOffering == null) {
        LoggingService.instance.logInfo(
          'SubscriptionService',
          'No current offering available',
        );
        return [];
      }

      final packages = currentOffering.availablePackages;

      LoggingService.instance.logInfo(
        'SubscriptionService',
        'Found ${packages.length} available packages',
      );

      return packages;
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        'SubscriptionService',
        'Failed to get available packages',
      );
      return [];
    }
  }

  /// Make a purchase
  Future<bool> makePurchase(Package package) async {
    _ensureInitialized();

    try {
      LoggingService.instance.logInfo(
        'SubscriptionService',
        'Starting purchase for package: ${package.identifier}',
      );

      final purchaseResult = await Purchases.purchasePackage(package);

      // Update subscription status
      _updateSubscriptionFromCustomerInfo(purchaseResult.customerInfo);

      // Track purchase in analytics (simplified - using logEvent instead)
      await AnalyticsService.instance.logEvent(
        name: 'subscription_purchase',
        parameters: {
          'currency': package.storeProduct.currencyCode,
          'value': package.storeProduct.price,
          'item_id': package.identifier,
          'item_name': package.storeProduct.title,
          'item_category': 'subscription',
        },
      );

      LoggingService.instance.logInfo(
        'SubscriptionService',
        'Purchase completed successfully',
      );

      return true;
    } on PlatformException catch (e) {
      final errorCode = PurchasesErrorHelper.getErrorCode(e);

      if (errorCode == PurchasesErrorCode.purchaseCancelledError) {
        LoggingService.instance.logInfo(
          'SubscriptionService',
          'Purchase was cancelled by user',
        );
      } else {
        await LoggingService.instance.logError(
          e,
          StackTrace.current,
          'SubscriptionService',
          'Purchase failed with error: ${e.message}',
        );
      }

      return false;
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        'SubscriptionService',
        'Unexpected error during purchase',
      );
      return false;
    }
  }

  /// Restore purchases
  Future<bool> restorePurchases() async {
    _ensureInitialized();

    try {
      LoggingService.instance.logInfo(
        'SubscriptionService',
        'Restoring purchases',
      );

      final customerInfo = await Purchases.restorePurchases();

      // Update subscription status
      _updateSubscriptionFromCustomerInfo(customerInfo);

      LoggingService.instance.logInfo(
        'SubscriptionService',
        'Purchases restored successfully',
      );

      return true;
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        'SubscriptionService',
        'Failed to restore purchases',
      );
      return false;
    }
  }

  /// Refresh customer info from RevenueCat
  Future<void> refreshCustomerInfo() async {
    _ensureInitialized();
    await _refreshCustomerInfo();
  }

  /// Set user ID for RevenueCat
  Future<void> setUserId(String userId) async {
    _ensureInitialized();

    try {
      await Purchases.logIn(userId);
      await _refreshCustomerInfo();
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        'SubscriptionService',
        'Failed to set user ID',
      );
    }
  }

  /// Log out current user
  Future<void> logOut() async {
    _ensureInitialized();

    try {
      await Purchases.logOut();
      _currentSubscription = UserSubscription.free();
      _subscriptionController.add(_currentSubscription);
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        'SubscriptionService',
        'Failed to log out user',
      );
    }
  }

  /// Internal method to refresh customer info
  Future<void> _refreshCustomerInfo() async {
    try {
      final customerInfo = await Purchases.getCustomerInfo();
      _updateSubscriptionFromCustomerInfo(customerInfo);
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        'SubscriptionService',
        'Failed to refresh customer info',
      );
    }
  }

  /// Handle customer info updates from RevenueCat
  void _onCustomerInfoUpdate(CustomerInfo customerInfo) {
    _updateSubscriptionFromCustomerInfo(customerInfo);
  }

  /// Convert RevenueCat CustomerInfo to UserSubscription
  void _updateSubscriptionFromCustomerInfo(CustomerInfo customerInfo) {
    try {
      final hasPremium = customerInfo.entitlements.active.containsKey(
        _premiumEntitlementId,
      );

      final entitlements = <SubscriptionEntitlement>[];
      if (hasPremium) {
        entitlements.add(SubscriptionEntitlement.premium);
      }

      String? subscriptionType;
      DateTime? expirationDate;
      DateTime? purchaseDate;
      String? originalTransactionId;
      bool willRenew = false;

      if (hasPremium) {
        final premiumEntitlement =
            customerInfo.entitlements.active[_premiumEntitlementId];
        if (premiumEntitlement != null) {
          // Parse date strings to DateTime objects
          final expDate = premiumEntitlement.expirationDate;
          if (expDate != null) {
            try {
              expirationDate = DateTime.parse(expDate);
            } catch (e) {
              // If parsing fails, leave as null
            }
          }

          try {
            purchaseDate = DateTime.parse(
              premiumEntitlement.latestPurchaseDate,
            );
          } catch (e) {
            // If parsing fails, leave as null
          }

          try {
            final parsedDate = DateTime.parse(
              premiumEntitlement.originalPurchaseDate,
            );
            originalTransactionId = parsedDate.millisecondsSinceEpoch
                .toString();
          } catch (e) {
            // If parsing fails, leave as null
          }

          willRenew = premiumEntitlement.willRenew;

          // Determine subscription type from product identifier
          final productId = premiumEntitlement.productIdentifier.toLowerCase();
          if (productId.contains('monthly')) {
            subscriptionType = 'monthly';
          } else if (productId.contains('annual') ||
              productId.contains('yearly')) {
            subscriptionType = 'annual';
          }
        }
      }

      _currentSubscription = UserSubscription(
        isActive: hasPremium,
        entitlements: entitlements,
        subscriptionType: subscriptionType,
        expirationDate: expirationDate,
        purchaseDate: purchaseDate,
        originalTransactionId: originalTransactionId,
        willRenew: willRenew,
        lastChecked: DateTime.now(),
      );

      _subscriptionController.add(_currentSubscription);

      LoggingService.instance.logInfo(
        'SubscriptionService',
        'Updated subscription status: ${_currentSubscription.userTier}',
      );
    } catch (e, stackTrace) {
      LoggingService.instance.logError(
        e,
        stackTrace,
        'SubscriptionService',
        'Failed to update subscription from customer info',
      );
    }
  }

  /// Ensure the service is initialized
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('SubscriptionService must be initialized before use');
    }
  }

  /// Dispose of resources
  void dispose() {
    _subscriptionController.close();
  }
}
