// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in upshift/test/services/notification_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i7;

import 'package:firebase_auth/firebase_auth.dart' as _i5;
import 'package:firebase_auth_platform_interface/firebase_auth_platform_interface.dart'
    as _i4;
import 'package:firebase_core/firebase_core.dart' as _i2;
import 'package:firebase_messaging/firebase_messaging.dart' as _i6;
import 'package:firebase_messaging_platform_interface/firebase_messaging_platform_interface.dart'
    as _i3;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i8;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeFirebaseApp_0 extends _i1.SmartFake implements _i2.FirebaseApp {
  _FakeFirebaseApp_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNotificationSettings_1 extends _i1.SmartFake
    implements _i3.NotificationSettings {
  _FakeNotificationSettings_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeActionCodeInfo_2 extends _i1.SmartFake
    implements _i4.ActionCodeInfo {
  _FakeActionCodeInfo_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUserCredential_3 extends _i1.SmartFake
    implements _i5.UserCredential {
  _FakeUserCredential_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeConfirmationResult_4 extends _i1.SmartFake
    implements _i5.ConfirmationResult {
  _FakeConfirmationResult_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePasswordValidationStatus_5 extends _i1.SmartFake
    implements _i4.PasswordValidationStatus {
  _FakePasswordValidationStatus_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUserMetadata_6 extends _i1.SmartFake implements _i4.UserMetadata {
  _FakeUserMetadata_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeMultiFactor_7 extends _i1.SmartFake implements _i5.MultiFactor {
  _FakeMultiFactor_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIdTokenResult_8 extends _i1.SmartFake implements _i4.IdTokenResult {
  _FakeIdTokenResult_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUser_9 extends _i1.SmartFake implements _i5.User {
  _FakeUser_9(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [FirebaseMessaging].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseMessaging extends _i1.Mock implements _i6.FirebaseMessaging {
  MockFirebaseMessaging() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.FirebaseApp get app =>
      (super.noSuchMethod(
            Invocation.getter(#app),
            returnValue: _FakeFirebaseApp_0(this, Invocation.getter(#app)),
          )
          as _i2.FirebaseApp);

  @override
  bool get isAutoInitEnabled =>
      (super.noSuchMethod(
            Invocation.getter(#isAutoInitEnabled),
            returnValue: false,
          )
          as bool);

  @override
  _i7.Stream<String> get onTokenRefresh =>
      (super.noSuchMethod(
            Invocation.getter(#onTokenRefresh),
            returnValue: _i7.Stream<String>.empty(),
          )
          as _i7.Stream<String>);

  @override
  set app(_i2.FirebaseApp? _app) => super.noSuchMethod(
    Invocation.setter(#app, _app),
    returnValueForMissingStub: null,
  );

  @override
  Map<dynamic, dynamic> get pluginConstants =>
      (super.noSuchMethod(
            Invocation.getter(#pluginConstants),
            returnValue: <dynamic, dynamic>{},
          )
          as Map<dynamic, dynamic>);

  @override
  _i7.Future<_i3.RemoteMessage?> getInitialMessage() =>
      (super.noSuchMethod(
            Invocation.method(#getInitialMessage, []),
            returnValue: _i7.Future<_i3.RemoteMessage?>.value(),
          )
          as _i7.Future<_i3.RemoteMessage?>);

  @override
  _i7.Future<void> deleteToken() =>
      (super.noSuchMethod(
            Invocation.method(#deleteToken, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<String?> getAPNSToken() =>
      (super.noSuchMethod(
            Invocation.method(#getAPNSToken, []),
            returnValue: _i7.Future<String?>.value(),
          )
          as _i7.Future<String?>);

  @override
  _i7.Future<String?> getToken({String? vapidKey}) =>
      (super.noSuchMethod(
            Invocation.method(#getToken, [], {#vapidKey: vapidKey}),
            returnValue: _i7.Future<String?>.value(),
          )
          as _i7.Future<String?>);

  @override
  _i7.Future<bool> isSupported() =>
      (super.noSuchMethod(
            Invocation.method(#isSupported, []),
            returnValue: _i7.Future<bool>.value(false),
          )
          as _i7.Future<bool>);

  @override
  _i7.Future<_i3.NotificationSettings> getNotificationSettings() =>
      (super.noSuchMethod(
            Invocation.method(#getNotificationSettings, []),
            returnValue: _i7.Future<_i3.NotificationSettings>.value(
              _FakeNotificationSettings_1(
                this,
                Invocation.method(#getNotificationSettings, []),
              ),
            ),
          )
          as _i7.Future<_i3.NotificationSettings>);

  @override
  _i7.Future<_i3.NotificationSettings> requestPermission({
    bool? alert = true,
    bool? announcement = false,
    bool? badge = true,
    bool? carPlay = false,
    bool? criticalAlert = false,
    bool? provisional = false,
    bool? sound = true,
    bool? providesAppNotificationSettings = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#requestPermission, [], {
              #alert: alert,
              #announcement: announcement,
              #badge: badge,
              #carPlay: carPlay,
              #criticalAlert: criticalAlert,
              #provisional: provisional,
              #sound: sound,
              #providesAppNotificationSettings: providesAppNotificationSettings,
            }),
            returnValue: _i7.Future<_i3.NotificationSettings>.value(
              _FakeNotificationSettings_1(
                this,
                Invocation.method(#requestPermission, [], {
                  #alert: alert,
                  #announcement: announcement,
                  #badge: badge,
                  #carPlay: carPlay,
                  #criticalAlert: criticalAlert,
                  #provisional: provisional,
                  #sound: sound,
                  #providesAppNotificationSettings:
                      providesAppNotificationSettings,
                }),
              ),
            ),
          )
          as _i7.Future<_i3.NotificationSettings>);

  @override
  _i7.Future<void> setAutoInitEnabled(bool? enabled) =>
      (super.noSuchMethod(
            Invocation.method(#setAutoInitEnabled, [enabled]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> setDeliveryMetricsExportToBigQuery(bool? enabled) =>
      (super.noSuchMethod(
            Invocation.method(#setDeliveryMetricsExportToBigQuery, [enabled]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> setForegroundNotificationPresentationOptions({
    bool? alert = false,
    bool? badge = false,
    bool? sound = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #setForegroundNotificationPresentationOptions,
              [],
              {#alert: alert, #badge: badge, #sound: sound},
            ),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> subscribeToTopic(String? topic) =>
      (super.noSuchMethod(
            Invocation.method(#subscribeToTopic, [topic]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> unsubscribeFromTopic(String? topic) =>
      (super.noSuchMethod(
            Invocation.method(#unsubscribeFromTopic, [topic]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);
}

/// A class which mocks [FirebaseAuth].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseAuth extends _i1.Mock implements _i5.FirebaseAuth {
  MockFirebaseAuth() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.FirebaseApp get app =>
      (super.noSuchMethod(
            Invocation.getter(#app),
            returnValue: _FakeFirebaseApp_0(this, Invocation.getter(#app)),
          )
          as _i2.FirebaseApp);

  @override
  set app(_i2.FirebaseApp? _app) => super.noSuchMethod(
    Invocation.setter(#app, _app),
    returnValueForMissingStub: null,
  );

  @override
  set tenantId(String? tenantId) => super.noSuchMethod(
    Invocation.setter(#tenantId, tenantId),
    returnValueForMissingStub: null,
  );

  @override
  set customAuthDomain(String? customAuthDomain) => super.noSuchMethod(
    Invocation.setter(#customAuthDomain, customAuthDomain),
    returnValueForMissingStub: null,
  );

  @override
  Map<dynamic, dynamic> get pluginConstants =>
      (super.noSuchMethod(
            Invocation.getter(#pluginConstants),
            returnValue: <dynamic, dynamic>{},
          )
          as Map<dynamic, dynamic>);

  @override
  _i7.Future<void> useAuthEmulator(
    String? host,
    int? port, {
    bool? automaticHostMapping = true,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #useAuthEmulator,
              [host, port],
              {#automaticHostMapping: automaticHostMapping},
            ),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> applyActionCode(String? code) =>
      (super.noSuchMethod(
            Invocation.method(#applyActionCode, [code]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<_i4.ActionCodeInfo> checkActionCode(String? code) =>
      (super.noSuchMethod(
            Invocation.method(#checkActionCode, [code]),
            returnValue: _i7.Future<_i4.ActionCodeInfo>.value(
              _FakeActionCodeInfo_2(
                this,
                Invocation.method(#checkActionCode, [code]),
              ),
            ),
          )
          as _i7.Future<_i4.ActionCodeInfo>);

  @override
  _i7.Future<void> confirmPasswordReset({
    required String? code,
    required String? newPassword,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#confirmPasswordReset, [], {
              #code: code,
              #newPassword: newPassword,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<_i5.UserCredential> createUserWithEmailAndPassword({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createUserWithEmailAndPassword, [], {
              #email: email,
              #password: password,
            }),
            returnValue: _i7.Future<_i5.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#createUserWithEmailAndPassword, [], {
                  #email: email,
                  #password: password,
                }),
              ),
            ),
          )
          as _i7.Future<_i5.UserCredential>);

  @override
  _i7.Future<_i5.UserCredential> getRedirectResult() =>
      (super.noSuchMethod(
            Invocation.method(#getRedirectResult, []),
            returnValue: _i7.Future<_i5.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#getRedirectResult, []),
              ),
            ),
          )
          as _i7.Future<_i5.UserCredential>);

  @override
  bool isSignInWithEmailLink(String? emailLink) =>
      (super.noSuchMethod(
            Invocation.method(#isSignInWithEmailLink, [emailLink]),
            returnValue: false,
          )
          as bool);

  @override
  _i7.Stream<_i5.User?> authStateChanges() =>
      (super.noSuchMethod(
            Invocation.method(#authStateChanges, []),
            returnValue: _i7.Stream<_i5.User?>.empty(),
          )
          as _i7.Stream<_i5.User?>);

  @override
  _i7.Stream<_i5.User?> idTokenChanges() =>
      (super.noSuchMethod(
            Invocation.method(#idTokenChanges, []),
            returnValue: _i7.Stream<_i5.User?>.empty(),
          )
          as _i7.Stream<_i5.User?>);

  @override
  _i7.Stream<_i5.User?> userChanges() =>
      (super.noSuchMethod(
            Invocation.method(#userChanges, []),
            returnValue: _i7.Stream<_i5.User?>.empty(),
          )
          as _i7.Stream<_i5.User?>);

  @override
  _i7.Future<void> sendPasswordResetEmail({
    required String? email,
    _i4.ActionCodeSettings? actionCodeSettings,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendPasswordResetEmail, [], {
              #email: email,
              #actionCodeSettings: actionCodeSettings,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendSignInLinkToEmail({
    required String? email,
    required _i4.ActionCodeSettings? actionCodeSettings,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendSignInLinkToEmail, [], {
              #email: email,
              #actionCodeSettings: actionCodeSettings,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> setLanguageCode(String? languageCode) =>
      (super.noSuchMethod(
            Invocation.method(#setLanguageCode, [languageCode]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> setSettings({
    bool? appVerificationDisabledForTesting = false,
    String? userAccessGroup,
    String? phoneNumber,
    String? smsCode,
    bool? forceRecaptchaFlow,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#setSettings, [], {
              #appVerificationDisabledForTesting:
                  appVerificationDisabledForTesting,
              #userAccessGroup: userAccessGroup,
              #phoneNumber: phoneNumber,
              #smsCode: smsCode,
              #forceRecaptchaFlow: forceRecaptchaFlow,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> setPersistence(_i4.Persistence? persistence) =>
      (super.noSuchMethod(
            Invocation.method(#setPersistence, [persistence]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<_i5.UserCredential> signInAnonymously() =>
      (super.noSuchMethod(
            Invocation.method(#signInAnonymously, []),
            returnValue: _i7.Future<_i5.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#signInAnonymously, []),
              ),
            ),
          )
          as _i7.Future<_i5.UserCredential>);

  @override
  _i7.Future<_i5.UserCredential> signInWithCredential(
    _i4.AuthCredential? credential,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithCredential, [credential]),
            returnValue: _i7.Future<_i5.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#signInWithCredential, [credential]),
              ),
            ),
          )
          as _i7.Future<_i5.UserCredential>);

  @override
  _i7.Future<_i5.UserCredential> signInWithCustomToken(String? token) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithCustomToken, [token]),
            returnValue: _i7.Future<_i5.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#signInWithCustomToken, [token]),
              ),
            ),
          )
          as _i7.Future<_i5.UserCredential>);

  @override
  _i7.Future<_i5.UserCredential> signInWithEmailAndPassword({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithEmailAndPassword, [], {
              #email: email,
              #password: password,
            }),
            returnValue: _i7.Future<_i5.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#signInWithEmailAndPassword, [], {
                  #email: email,
                  #password: password,
                }),
              ),
            ),
          )
          as _i7.Future<_i5.UserCredential>);

  @override
  _i7.Future<_i5.UserCredential> signInWithEmailLink({
    required String? email,
    required String? emailLink,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithEmailLink, [], {
              #email: email,
              #emailLink: emailLink,
            }),
            returnValue: _i7.Future<_i5.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#signInWithEmailLink, [], {
                  #email: email,
                  #emailLink: emailLink,
                }),
              ),
            ),
          )
          as _i7.Future<_i5.UserCredential>);

  @override
  _i7.Future<_i5.UserCredential> signInWithProvider(
    _i4.AuthProvider? provider,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithProvider, [provider]),
            returnValue: _i7.Future<_i5.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#signInWithProvider, [provider]),
              ),
            ),
          )
          as _i7.Future<_i5.UserCredential>);

  @override
  _i7.Future<_i5.ConfirmationResult> signInWithPhoneNumber(
    String? phoneNumber, [
    _i5.RecaptchaVerifier? verifier,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithPhoneNumber, [phoneNumber, verifier]),
            returnValue: _i7.Future<_i5.ConfirmationResult>.value(
              _FakeConfirmationResult_4(
                this,
                Invocation.method(#signInWithPhoneNumber, [
                  phoneNumber,
                  verifier,
                ]),
              ),
            ),
          )
          as _i7.Future<_i5.ConfirmationResult>);

  @override
  _i7.Future<_i5.UserCredential> signInWithPopup(_i4.AuthProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithPopup, [provider]),
            returnValue: _i7.Future<_i5.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#signInWithPopup, [provider]),
              ),
            ),
          )
          as _i7.Future<_i5.UserCredential>);

  @override
  _i7.Future<void> signInWithRedirect(_i4.AuthProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithRedirect, [provider]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<String> verifyPasswordResetCode(String? code) =>
      (super.noSuchMethod(
            Invocation.method(#verifyPasswordResetCode, [code]),
            returnValue: _i7.Future<String>.value(
              _i8.dummyValue<String>(
                this,
                Invocation.method(#verifyPasswordResetCode, [code]),
              ),
            ),
          )
          as _i7.Future<String>);

  @override
  _i7.Future<void> verifyPhoneNumber({
    String? phoneNumber,
    _i4.PhoneMultiFactorInfo? multiFactorInfo,
    required _i4.PhoneVerificationCompleted? verificationCompleted,
    required _i4.PhoneVerificationFailed? verificationFailed,
    required _i4.PhoneCodeSent? codeSent,
    required _i4.PhoneCodeAutoRetrievalTimeout? codeAutoRetrievalTimeout,
    String? autoRetrievedSmsCodeForTesting,
    Duration? timeout = const Duration(seconds: 30),
    int? forceResendingToken,
    _i4.MultiFactorSession? multiFactorSession,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#verifyPhoneNumber, [], {
              #phoneNumber: phoneNumber,
              #multiFactorInfo: multiFactorInfo,
              #verificationCompleted: verificationCompleted,
              #verificationFailed: verificationFailed,
              #codeSent: codeSent,
              #codeAutoRetrievalTimeout: codeAutoRetrievalTimeout,
              #autoRetrievedSmsCodeForTesting: autoRetrievedSmsCodeForTesting,
              #timeout: timeout,
              #forceResendingToken: forceResendingToken,
              #multiFactorSession: multiFactorSession,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> revokeTokenWithAuthorizationCode(
    String? authorizationCode,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#revokeTokenWithAuthorizationCode, [
              authorizationCode,
            ]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> signOut() =>
      (super.noSuchMethod(
            Invocation.method(#signOut, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> initializeRecaptchaConfig() =>
      (super.noSuchMethod(
            Invocation.method(#initializeRecaptchaConfig, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<_i4.PasswordValidationStatus> validatePassword(
    _i5.FirebaseAuth? auth,
    String? password,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#validatePassword, [auth, password]),
            returnValue: _i7.Future<_i4.PasswordValidationStatus>.value(
              _FakePasswordValidationStatus_5(
                this,
                Invocation.method(#validatePassword, [auth, password]),
              ),
            ),
          )
          as _i7.Future<_i4.PasswordValidationStatus>);
}

/// A class which mocks [User].
///
/// See the documentation for Mockito's code generation for more information.
class MockUser extends _i1.Mock implements _i5.User {
  MockUser() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get emailVerified =>
      (super.noSuchMethod(Invocation.getter(#emailVerified), returnValue: false)
          as bool);

  @override
  bool get isAnonymous =>
      (super.noSuchMethod(Invocation.getter(#isAnonymous), returnValue: false)
          as bool);

  @override
  _i4.UserMetadata get metadata =>
      (super.noSuchMethod(
            Invocation.getter(#metadata),
            returnValue: _FakeUserMetadata_6(
              this,
              Invocation.getter(#metadata),
            ),
          )
          as _i4.UserMetadata);

  @override
  List<_i4.UserInfo> get providerData =>
      (super.noSuchMethod(
            Invocation.getter(#providerData),
            returnValue: <_i4.UserInfo>[],
          )
          as List<_i4.UserInfo>);

  @override
  String get uid =>
      (super.noSuchMethod(
            Invocation.getter(#uid),
            returnValue: _i8.dummyValue<String>(this, Invocation.getter(#uid)),
          )
          as String);

  @override
  _i5.MultiFactor get multiFactor =>
      (super.noSuchMethod(
            Invocation.getter(#multiFactor),
            returnValue: _FakeMultiFactor_7(
              this,
              Invocation.getter(#multiFactor),
            ),
          )
          as _i5.MultiFactor);

  @override
  _i7.Future<void> delete() =>
      (super.noSuchMethod(
            Invocation.method(#delete, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<String?> getIdToken([bool? forceRefresh = false]) =>
      (super.noSuchMethod(
            Invocation.method(#getIdToken, [forceRefresh]),
            returnValue: _i7.Future<String?>.value(),
          )
          as _i7.Future<String?>);

  @override
  _i7.Future<_i4.IdTokenResult> getIdTokenResult([
    bool? forceRefresh = false,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#getIdTokenResult, [forceRefresh]),
            returnValue: _i7.Future<_i4.IdTokenResult>.value(
              _FakeIdTokenResult_8(
                this,
                Invocation.method(#getIdTokenResult, [forceRefresh]),
              ),
            ),
          )
          as _i7.Future<_i4.IdTokenResult>);

  @override
  _i7.Future<_i5.UserCredential> linkWithCredential(
    _i4.AuthCredential? credential,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#linkWithCredential, [credential]),
            returnValue: _i7.Future<_i5.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#linkWithCredential, [credential]),
              ),
            ),
          )
          as _i7.Future<_i5.UserCredential>);

  @override
  _i7.Future<_i5.UserCredential> linkWithProvider(_i4.AuthProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#linkWithProvider, [provider]),
            returnValue: _i7.Future<_i5.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#linkWithProvider, [provider]),
              ),
            ),
          )
          as _i7.Future<_i5.UserCredential>);

  @override
  _i7.Future<_i5.UserCredential> reauthenticateWithProvider(
    _i4.AuthProvider? provider,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#reauthenticateWithProvider, [provider]),
            returnValue: _i7.Future<_i5.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#reauthenticateWithProvider, [provider]),
              ),
            ),
          )
          as _i7.Future<_i5.UserCredential>);

  @override
  _i7.Future<_i5.UserCredential> reauthenticateWithPopup(
    _i4.AuthProvider? provider,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#reauthenticateWithPopup, [provider]),
            returnValue: _i7.Future<_i5.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#reauthenticateWithPopup, [provider]),
              ),
            ),
          )
          as _i7.Future<_i5.UserCredential>);

  @override
  _i7.Future<void> reauthenticateWithRedirect(_i4.AuthProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#reauthenticateWithRedirect, [provider]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<_i5.UserCredential> linkWithPopup(_i4.AuthProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#linkWithPopup, [provider]),
            returnValue: _i7.Future<_i5.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#linkWithPopup, [provider]),
              ),
            ),
          )
          as _i7.Future<_i5.UserCredential>);

  @override
  _i7.Future<void> linkWithRedirect(_i4.AuthProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#linkWithRedirect, [provider]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<_i5.ConfirmationResult> linkWithPhoneNumber(
    String? phoneNumber, [
    _i5.RecaptchaVerifier? verifier,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#linkWithPhoneNumber, [phoneNumber, verifier]),
            returnValue: _i7.Future<_i5.ConfirmationResult>.value(
              _FakeConfirmationResult_4(
                this,
                Invocation.method(#linkWithPhoneNumber, [
                  phoneNumber,
                  verifier,
                ]),
              ),
            ),
          )
          as _i7.Future<_i5.ConfirmationResult>);

  @override
  _i7.Future<_i5.UserCredential> reauthenticateWithCredential(
    _i4.AuthCredential? credential,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#reauthenticateWithCredential, [credential]),
            returnValue: _i7.Future<_i5.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#reauthenticateWithCredential, [credential]),
              ),
            ),
          )
          as _i7.Future<_i5.UserCredential>);

  @override
  _i7.Future<void> reload() =>
      (super.noSuchMethod(
            Invocation.method(#reload, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendEmailVerification([
    _i4.ActionCodeSettings? actionCodeSettings,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#sendEmailVerification, [actionCodeSettings]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<_i5.User> unlink(String? providerId) =>
      (super.noSuchMethod(
            Invocation.method(#unlink, [providerId]),
            returnValue: _i7.Future<_i5.User>.value(
              _FakeUser_9(this, Invocation.method(#unlink, [providerId])),
            ),
          )
          as _i7.Future<_i5.User>);

  @override
  _i7.Future<void> updatePassword(String? newPassword) =>
      (super.noSuchMethod(
            Invocation.method(#updatePassword, [newPassword]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> updatePhoneNumber(
    _i4.PhoneAuthCredential? phoneCredential,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updatePhoneNumber, [phoneCredential]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> updateDisplayName(String? displayName) =>
      (super.noSuchMethod(
            Invocation.method(#updateDisplayName, [displayName]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> updatePhotoURL(String? photoURL) =>
      (super.noSuchMethod(
            Invocation.method(#updatePhotoURL, [photoURL]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> updateProfile({String? displayName, String? photoURL}) =>
      (super.noSuchMethod(
            Invocation.method(#updateProfile, [], {
              #displayName: displayName,
              #photoURL: photoURL,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> verifyBeforeUpdateEmail(
    String? newEmail, [
    _i4.ActionCodeSettings? actionCodeSettings,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#verifyBeforeUpdateEmail, [
              newEmail,
              actionCodeSettings,
            ]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);
}

/// A class which mocks [NotificationSettings].
///
/// See the documentation for Mockito's code generation for more information.
class MockNotificationSettings extends _i1.Mock
    implements _i3.NotificationSettings {
  MockNotificationSettings() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.AppleNotificationSetting get alert =>
      (super.noSuchMethod(
            Invocation.getter(#alert),
            returnValue: _i3.AppleNotificationSetting.disabled,
          )
          as _i3.AppleNotificationSetting);

  @override
  _i3.AppleNotificationSetting get announcement =>
      (super.noSuchMethod(
            Invocation.getter(#announcement),
            returnValue: _i3.AppleNotificationSetting.disabled,
          )
          as _i3.AppleNotificationSetting);

  @override
  _i3.AuthorizationStatus get authorizationStatus =>
      (super.noSuchMethod(
            Invocation.getter(#authorizationStatus),
            returnValue: _i3.AuthorizationStatus.authorized,
          )
          as _i3.AuthorizationStatus);

  @override
  _i3.AppleNotificationSetting get timeSensitive =>
      (super.noSuchMethod(
            Invocation.getter(#timeSensitive),
            returnValue: _i3.AppleNotificationSetting.disabled,
          )
          as _i3.AppleNotificationSetting);

  @override
  _i3.AppleNotificationSetting get criticalAlert =>
      (super.noSuchMethod(
            Invocation.getter(#criticalAlert),
            returnValue: _i3.AppleNotificationSetting.disabled,
          )
          as _i3.AppleNotificationSetting);

  @override
  _i3.AppleNotificationSetting get badge =>
      (super.noSuchMethod(
            Invocation.getter(#badge),
            returnValue: _i3.AppleNotificationSetting.disabled,
          )
          as _i3.AppleNotificationSetting);

  @override
  _i3.AppleNotificationSetting get carPlay =>
      (super.noSuchMethod(
            Invocation.getter(#carPlay),
            returnValue: _i3.AppleNotificationSetting.disabled,
          )
          as _i3.AppleNotificationSetting);

  @override
  _i3.AppleNotificationSetting get lockScreen =>
      (super.noSuchMethod(
            Invocation.getter(#lockScreen),
            returnValue: _i3.AppleNotificationSetting.disabled,
          )
          as _i3.AppleNotificationSetting);

  @override
  _i3.AppleNotificationSetting get notificationCenter =>
      (super.noSuchMethod(
            Invocation.getter(#notificationCenter),
            returnValue: _i3.AppleNotificationSetting.disabled,
          )
          as _i3.AppleNotificationSetting);

  @override
  _i3.AppleShowPreviewSetting get showPreviews =>
      (super.noSuchMethod(
            Invocation.getter(#showPreviews),
            returnValue: _i3.AppleShowPreviewSetting.always,
          )
          as _i3.AppleShowPreviewSetting);

  @override
  _i3.AppleNotificationSetting get sound =>
      (super.noSuchMethod(
            Invocation.getter(#sound),
            returnValue: _i3.AppleNotificationSetting.disabled,
          )
          as _i3.AppleNotificationSetting);

  @override
  _i3.AppleNotificationSetting get providesAppNotificationSettings =>
      (super.noSuchMethod(
            Invocation.getter(#providesAppNotificationSettings),
            returnValue: _i3.AppleNotificationSetting.disabled,
          )
          as _i3.AppleNotificationSetting);
}
