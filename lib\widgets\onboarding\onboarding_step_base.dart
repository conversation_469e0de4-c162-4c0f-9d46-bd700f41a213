import 'package:flutter/material.dart';
import '../../theme/theme.dart';
import 'onboarding_widgets.dart';

/// Base widget for onboarding steps with common header and footer elements
class OnboardingStepBase extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final String title;
  final String subtitle;
  final Widget child;
  final VoidCallback? onBack;
  final VoidCallback? onSkip;
  final VoidCallback? onContinue;
  final bool canContinue;
  final bool isLoading;
  final String continueButtonText;

  const OnboardingStepBase({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    required this.title,
    required this.subtitle,
    required this.child,
    this.onBack,
    this.onSkip,
    this.onContinue,
    this.canContinue = false,
    this.isLoading = false,
    this.continueButtonText = 'Continue',
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.bg0,
      body: SafeArea(
        child: Column(
          children: [
            // Header with back button, progress, and skip
            _buildHeader(),
            
            // Content area
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 32),
                    
                    // Title
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.w700,
                        height: 1.1,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Subtitle
                    Text(
                      subtitle,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                        height: 1.4,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    
                    const SizedBox(height: 32),
                    
                    // Step content
                    Expanded(child: child),
                  ],
                ),
              ),
            ),
            
            // Continue button
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Row(
        children: [
          // Back button
          if (onBack != null)
            GestureDetector(
              onTap: onBack,
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppColors.bg2,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.arrow_back,
                  color: AppColors.textPrimary,
                  size: 20,
                ),
              ),
            )
          else
            const SizedBox(width: 40),
          
          const SizedBox(width: 16),
          
          // Progress bar
          Expanded(
            child: ProgressBar(
              currentStep: currentStep,
              totalSteps: totalSteps,
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Skip button
          if (onSkip != null)
            GestureDetector(
              onTap: onSkip,
              child: const Text(
                'Skip',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textSecondary,
                ),
              ),
            )
          else
            const SizedBox(width: 40),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: CoachButton(
        text: continueButtonText,
        onPressed: canContinue && !isLoading ? onContinue : null,
        isLoading: isLoading,
        variant: CoachButtonVariant.primary,
      ),
    );
  }
}
