// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAxpc7riwe41mDHmEe41uzGn1eRssUPP6Q',
    appId: '1:49879574938:android:3335ca6a51c869d6d67223',
    messagingSenderId: '49879574938',
    projectId: 'upshift-life',
    storageBucket: 'upshift-life.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCxuuwtfbJI9rS5ZUGj8QwZGpK4M-arP8I',
    appId: '1:49879574938:ios:aadc861de9312f8cd67223',
    messagingSenderId: '49879574938',
    projectId: 'upshift-life',
    storageBucket: 'upshift-life.firebasestorage.app',
    androidClientId: '49879574938-eba9mp7200isdh3dkima11q90l5a6sj7.apps.googleusercontent.com',
    iosClientId: '49879574938-5ll5sac255f4c8pdfdu8jdvvm7o8q2pg.apps.googleusercontent.com',
    iosBundleId: 'life.upshift.upshiftapp',
  );
}
