import 'package:flutter/material.dart';
import 'package:upshift/models/models.dart' as models;
import 'package:upshift/theme/theme.dart';
import 'package:upshift/widgets/persona_video_modal.dart';

/// A reusable card widget for displaying SystemPersona information
///
/// This widget provides a vertical layout with:
/// - Centered persona avatar/image
/// - Persona name and catchphrase below the image
/// - Consistent theming and spacing
/// - Tap handling with visual feedback
/// - Support for selection states
/// - Optional video playback on avatar tap
class PersonaCard extends StatelessWidget {
  final models.SystemPersona persona;
  final bool isSelected;
  final VoidCallback? onTap;
  final VoidCallback? onAvatarTap;
  final bool showSelectionIndicator;
  final EdgeInsets? margin;
  final double? width;
  final double? height;

  const PersonaCard({
    super.key,
    required this.persona,
    this.isSelected = false,
    this.onTap,
    this.onAvatarTap,
    this.showSelectionIndicator = false,
    this.margin,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    final cardHeight = height ?? 500.0;
    return Container(
      width: width,
      height: cardHeight,
      margin: margin ?? const EdgeInsets.only(bottom: AppDimensions.spacingM),
      child: Card(
        elevation: AppDimensions.elevationS,
        child: InkWell(
          onTap: onTap,
          borderRadius: AppDimensions.borderRadiusL,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: AppDimensions.borderRadiusL,
              border: isSelected
                  ? Border.all(color: context.colorScheme.primary, width: 2)
                  : Border.all(color: Colors.transparent, width: 2),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Avatar section - fixed height to ensure proper 2:3 aspect ratio
                SizedBox(
                  height: 380.0, // Fixed height to ensure proper aspect ratio
                  child: _buildAvatar(context),
                ),
                SizedBox(height: AppDimensions.spacingM),

                // Name with coaching style and approach tags
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppDimensions.spacingM,
                  ),
                  child: _buildNameWithTags(context),
                ),

                SizedBox(height: AppDimensions.spacingS),

                // Catchphrase
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppDimensions.spacingM,
                  ),
                  child: _builCatchphrase(context),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar(BuildContext context) {
    return GestureDetector(
      onTap: onAvatarTap ?? () => _showVideoModal(context),
      child: AspectRatio(
        aspectRatio: 2 / 3, // 2:3 aspect ratio for 1024x1536 images
        child: Container(
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: AppDimensions.borderRadiusL,
            boxShadow: [
              BoxShadow(
                color: context.colorScheme.shadow.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Stack(
            children: [
              Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: AppDimensions.borderRadiusL,
                  color: context.colorScheme.primaryContainer,
                  image: _getAvatarImage() != null
                      ? DecorationImage(
                          image: _getAvatarImage()!,
                          fit: BoxFit.cover,
                        )
                      : null,
                ),
                child: _getAvatarImage() == null
                    ? Icon(
                        AppIcons.profile,
                        size: 60,
                        color: context.colorScheme.onPrimaryContainer,
                      )
                    : null,
              ),
              // Play button overlay
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: AppDimensions.borderRadiusL,
                    color: Colors.black.withValues(alpha: 0.3),
                  ),
                  child: Icon(
                    Icons.play_circle_outline,
                    size: 40,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showVideoModal(BuildContext context) {
    // Only show video modal if videoUrl is non-empty (not just whitespace)
    if (persona.videoUrl.trim().isNotEmpty) {
      PersonaVideoModal.show(context, persona: persona);
    }
  }

  Widget _buildNameWithTags(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Name with selection indicator
        Row(
          children: [
            Expanded(
              child: Text(
                persona.name,
                style: context.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (showSelectionIndicator && isSelected) ...[
              SizedBox(width: AppDimensions.spacingS),
              Icon(
                Icons.check_circle,
                color: context.colorScheme.primary,
                size: 20,
              ),
            ],
          ],
        ),

        // Coaching style and approach tags on the same line
        SizedBox(height: AppDimensions.spacingS),
        Wrap(
          spacing: 6,
          runSpacing: 4,
          children: [
            _buildCoachingStyleTag(context, persona.coachingStyle),
            _buildApproachTag(context, persona.approach),
          ],
        ),
      ],
    );
  }

  Widget _builCatchphrase(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Name with selection indicator
        Row(
          children: [
            Expanded(
              child: Text(
                persona.catchphrase,
                style: context.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w200,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCoachingStyleTag(BuildContext context, String coachingStyle) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: context.colorScheme.tertiary.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: context.colorScheme.tertiary.withValues(alpha: 0.4),
          width: 0.5,
        ),
      ),
      child: Text(
        coachingStyle.replaceAll('-', ' ').toLowerCase(),
        style: context.textTheme.bodySmall?.copyWith(
          color: context.colorScheme.tertiary,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildApproachTag(BuildContext context, String approach) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: context.colorScheme.tertiary.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: context.colorScheme.tertiary.withValues(alpha: 0.4),
          width: 0.5,
        ),
      ),
      child: Text(
        approach.replaceAll('-', ' ').toLowerCase(),
        style: context.textTheme.bodySmall?.copyWith(
          color: context.colorScheme.tertiary,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  ImageProvider? _getAvatarImage() {
    if (persona.avatarUrl.isEmpty) {
      return null;
    }

    if (persona.avatarUrl.startsWith('assets/')) {
      return AssetImage(persona.avatarUrl);
    } else {
      return NetworkImage(persona.avatarUrl);
    }
  }
}
