import 'package:flutter/material.dart';
import 'package:upshift/models/models.dart';
import 'package:upshift/theme/theme.dart';
import 'package:upshift/pages/chat_page.dart';

/// A reusable chat card widget that displays chat information
///
/// This widget provides:
/// - Chat title and persona information
/// - Last updated timestamp
/// - Navigation to chat page
/// - Consistent styling across the app
class ChatCard extends StatelessWidget {
  final Chat chat;
  final SystemPersona? systemPersona;
  final VoidCallback? onTap;
  final VoidCallback? onRefresh;
  final bool fullWidth;

  const ChatCard({
    super.key,
    required this.chat,
    this.systemPersona,
    this.onTap,
    this.onRefresh,
    this.fullWidth = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: fullWidth
          ? AppDimensions.paddingVerticalXs
          : AppDimensions.paddingHorizontalM.add(
              AppDimensions.paddingVerticalXs,
            ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          if (onTap != null) {
            onTap!();
          } else {
            _navigateToChat(context);
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.spacingM),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Avatar with proper 2:3 aspect ratio
              _buildChatAvatar(context),
              const SizedBox(width: 16),
              // Content area
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Title
                    Text(
                      chat.title.isEmpty ? 'New Chat' : chat.title,
                      style: context.textTheme.titleMedium,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    // Subtitle
                    _buildChatSubtitle(context),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              // Trailing icon
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  chat.isCompleted
                      ? Icon(
                          Icons.task_alt,
                          color: context.colorScheme.primary,
                          size: 20,
                        )
                      : Icon(
                          AppIcons.right,
                          color: context.colorScheme.onSurface.withValues(
                            alpha: 0.6,
                          ),
                          size: 24,
                        ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChatAvatar(BuildContext context) {
    if (systemPersona?.avatarUrl != null &&
        systemPersona!.avatarUrl.isNotEmpty) {
      return Container(
        width: 60, // 2:3 aspect ratio base width
        height: 90, // 2:3 aspect ratio height (60 * 1.5)
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: context.colorScheme.primaryContainer,
          image: DecorationImage(
            image: AssetImage(systemPersona!.avatarUrl),
            fit: BoxFit.cover,
          ),
        ),
      );
    } else {
      return Container(
        width: 60,
        height: 90,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: context.colorScheme.primaryContainer,
        ),
        child: Icon(
          AppIcons.chat,
          color: context.colorScheme.onPrimaryContainer,
          size: 24,
        ),
      );
    }
  }

  Widget _buildChatSubtitle(BuildContext context) {
    final lastUpdatedText =
        'Last updated: ${_formatDate(chat.lastUpdatedDate)}';

    if (systemPersona != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            systemPersona!.name,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            lastUpdatedText,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      );
    } else {
      return Text(
        lastUpdatedText,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
        ),
      );
    }
  }

  void _navigateToChat(BuildContext context) {
    if (chat.id != null) {
      Navigator.of(context)
          .push(MaterialPageRoute(builder: (context) => ChatPage(chat: chat)))
          .then((_) {
            // Refresh callback when returning from chat
            if (onRefresh != null) {
              onRefresh!();
            }
          });
    } else {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Chat ID not available')));
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }
}
