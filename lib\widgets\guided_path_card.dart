import 'package:flutter/material.dart';
import '../models/models.dart';
import '../pages/upsell_page.dart';

class GuidedPathCard extends StatelessWidget {
  final GuidedPath guidedPath;
  final UserPathProgress? userProgress;
  final bool isAccessible;
  final VoidCallback? onTap;
  final VoidCallback? onContinue;

  const GuidedPathCard({
    super.key,
    required this.guidedPath,
    this.userProgress,
    required this.isAccessible,
    this.onTap,
    this.onContinue,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      child: InkWell(
        onTap: isAccessible
            ? onTap
            : () => Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const UpsellPage()),
              ),
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: !isAccessible
                    ? colorScheme.surface.withValues(alpha: 0.3)
                    : null,
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header with title and tags
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                guidedPath.name,
                                style: theme.textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: isAccessible
                                      ? colorScheme.onSurface
                                      : colorScheme.onSurface.withValues(
                                          alpha: 0.6,
                                        ),
                                ),
                              ),
                              const SizedBox(height: 8),
                              // Tags from metadata
                              _buildTagsSection(context),
                            ],
                          ),
                        ),
                        // Completion indicator (only for accessible paths)
                        if (isAccessible) _buildCompletionIndicator(context),
                      ],
                    ),

                    const SizedBox(height: 12),

                    // Description (dynamic height)
                    Text(
                      guidedPath.description,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: isAccessible
                            ? colorScheme.onSurface.withValues(alpha: 0.8)
                            : colorScheme.onSurface.withValues(alpha: 0.5),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Progress bar (if user has started)
                    if (userProgress != null) ...[
                      _buildProgressSection(context),
                    ],
                  ],
                ),
              ),
            ),

            // Lock icon in top-right corner for non-accessible paths
            if (!isAccessible)
              Positioned(
                top: 12,
                right: 12,
                child: Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: colorScheme.secondary,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(Icons.lock, color: Colors.white, size: 16),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressSection(BuildContext context) {
    if (userProgress == null) return const SizedBox.shrink();

    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final progress = userProgress!.getProgressPercentage(guidedPath.stepCount);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              _getProgressText(),
              style: theme.textTheme.labelMedium?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              '${(progress * 100).round()}%',
              style: theme.textTheme.labelMedium?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: colorScheme.primary.withValues(alpha: 0.2),
          valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
        ),
      ],
    );
  }

  Widget _buildTagsSection(BuildContext context) {
    final tags = _getTagsFromMetadata();
    if (tags.isEmpty) return const SizedBox.shrink();

    return Wrap(
      spacing: 6,
      runSpacing: 4,
      children: tags.map((tag) => _buildTag(context, tag)).toList(),
    );
  }

  Widget _buildTag(BuildContext context, String tag) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: colorScheme.tertiary.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.tertiary.withValues(alpha: 0.4),
          width: 0.5,
        ),
      ),
      child: Text(
        tag.replaceAll('-', ' ').toLowerCase(),
        style: theme.textTheme.bodySmall?.copyWith(
          color: colorScheme.tertiary,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildCompletionIndicator(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    if (userProgress?.isCompleted == true) {
      return Container(
        padding: const EdgeInsets.all(6),
        decoration: BoxDecoration(color: Colors.green, shape: BoxShape.circle),
        child: Icon(Icons.task_alt, color: Colors.white, size: 16),
      );
    }

    // Check if path is in progress (has started steps but not completed)
    final hasStartedSteps =
        userProgress?.stepProgress?.values.any(
          (progress) => progress.startedDate != null || progress.isCompleted,
        ) ??
        false;

    if (hasStartedSteps && userProgress?.isCompleted != true) {
      return Container(
        padding: const EdgeInsets.all(6),
        decoration: BoxDecoration(
          color: colorScheme.primary,
          shape: BoxShape.circle,
        ),
        child: Icon(Icons.play_arrow, color: Colors.white, size: 16),
      );
    }

    return const SizedBox.shrink();
  }

  List<String> _getTagsFromMetadata() {
    if (guidedPath.metadata == null) return [];
    final tags = guidedPath.metadata!['tags'];
    if (tags is List) {
      return tags.cast<String>();
    }
    return [];
  }

  String _getProgressText() {
    if (userProgress == null) return 'Not Started';

    if (userProgress!.isCompleted) {
      return 'Completed';
    }

    // Check if any steps have been started
    final hasStartedSteps =
        userProgress!.stepProgress?.values.any(
          (progress) => progress.startedDate != null || progress.isCompleted,
        ) ??
        false;

    return hasStartedSteps ? 'In Progress' : 'Not Started';
  }
}
