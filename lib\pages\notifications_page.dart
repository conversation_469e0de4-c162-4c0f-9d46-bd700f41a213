import 'package:firebase_auth/firebase_auth.dart' as auth;
import 'package:flutter/material.dart';
import '../theme/theme.dart';
import '../services/firestore.dart';
import '../services/notification_service.dart';
import '../models/models.dart';

class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage> {
  bool _isLoading = true;
  bool _isSaving = false;
  String? _error;

  User? _currentUser;
  NotificationPreferences? _currentPreferences;
  NotificationPreferences? _modifiedPreferences;

  // State tracking for modifications
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _loadNotificationData();
  }

  Future<void> _loadNotificationData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final firebaseUser = auth.FirebaseAuth.instance.currentUser;
      if (firebaseUser == null) {
        throw Exception('User not authenticated');
      }

      // Load user data
      final user = await FirestoreService.getUser(firebaseUser.uid);
      if (user == null) {
        throw Exception('User not found');
      }

      setState(() {
        _currentUser = user;
        _currentPreferences =
            user.notificationPreferences ??
            NotificationPreferences.defaultPreferences();
        _modifiedPreferences = _currentPreferences;
        _hasChanges = false;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _saveNotificationPreferences() async {
    if (_modifiedPreferences == null || _currentUser == null) return;

    setState(() {
      _isSaving = true;
    });

    try {
      await FirestoreService.updateUserNotificationPreferences(
        userId: _currentUser!.id,
        preferences: _modifiedPreferences!,
      );

      // Update topic subscriptions based on preferences
      await _updateTopicSubscriptions();

      // Reload data to reflect changes
      await _loadNotificationData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Notification preferences updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Failed to update notification preferences: $e';
        });
      }
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  Future<void> _updateTopicSubscriptions() async {
    if (_modifiedPreferences == null) return;

    try {
      final notificationService = NotificationService.instance;

      // Only manage subscriptions if push notifications are enabled
      if (_modifiedPreferences!.enablePushNotifications) {
        // Subscribe/unsubscribe from topics based on preferences
        for (final entry in _modifiedPreferences!.topicPreferences.entries) {
          final topic = entry.key;
          final isEnabled = entry.value;

          if (isEnabled) {
            await notificationService.subscribeToTopic(topic);
          } else {
            await notificationService.unsubscribeFromTopic(topic);
          }
        }
      } else {
        // Unsubscribe from all topics if push notifications are disabled
        for (final topic in _modifiedPreferences!.topicPreferences.keys) {
          await notificationService.unsubscribeFromTopic(topic);
        }
      }
    } catch (e) {
      // Log error but don't fail the save operation
      debugPrint('Failed to update topic subscriptions: $e');
    }
  }

  void _updatePreferences(NotificationPreferences newPreferences) {
    setState(() {
      _modifiedPreferences = newPreferences;
      _hasChanges = _currentPreferences != _modifiedPreferences;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Settings'),
        backgroundColor: context.colorScheme.surface,
        foregroundColor: context.colorScheme.onSurface,
        elevation: 0,
        actions: [
          if (_hasChanges)
            TextButton(
              onPressed: _isSaving ? null : _saveNotificationPreferences,
              child: _isSaving
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('Save'),
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
          ? _buildErrorWidget()
          : _buildNotificationSettings(),
    );
  }

  Widget _buildErrorWidget() {
    return Padding(
      padding: AppDimensions.paddingL,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: context.colorScheme.error),
          SizedBox(height: AppDimensions.spacingM),
          SelectableText.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: 'Error loading notification settings:\n',
                  style: context.textTheme.titleMedium?.copyWith(
                    color: context.colorScheme.error,
                  ),
                ),
                TextSpan(
                  text: _error!,
                  style: context.textTheme.bodyMedium?.copyWith(
                    color: context.colorScheme.error,
                  ),
                ),
              ],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: AppDimensions.spacingL),
          ElevatedButton(
            onPressed: _loadNotificationData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationSettings() {
    if (_modifiedPreferences == null) return const SizedBox.shrink();

    return SingleChildScrollView(
      padding: AppDimensions.paddingL,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Master toggles section
          _buildMasterTogglesCard(),
          SizedBox(height: AppDimensions.spacingL),

          // Topic preferences section
          if (_modifiedPreferences!.enablePushNotifications)
            _buildTopicPreferencesCard(),
        ],
      ),
    );
  }

  Widget _buildMasterTogglesCard() {
    return Card(
      child: Padding(
        padding: AppDimensions.paddingL,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notification Preferences',
              style: context.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: AppDimensions.spacingM),

            // Push notifications toggle
            _buildToggleTile(
              title: 'Push Notifications',
              subtitle: 'Receive notifications on this device',
              value: _modifiedPreferences!.enablePushNotifications,
              onChanged: (value) {
                _updatePreferences(
                  _modifiedPreferences!.copyWith(
                    enablePushNotifications: value,
                  ),
                );
              },
              icon: Icons.notifications,
            ),

            const Divider(),

            // Email notifications toggle
            _buildToggleTile(
              title: 'Email Notifications',
              subtitle: 'Receive notifications via email',
              value: _modifiedPreferences!.enableEmailNotifications,
              onChanged: (value) {
                _updatePreferences(
                  _modifiedPreferences!.copyWith(
                    enableEmailNotifications: value,
                  ),
                );
              },
              icon: Icons.email,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopicPreferencesCard() {
    return Card(
      child: Padding(
        padding: AppDimensions.paddingL,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notification Types',
              style: context.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: AppDimensions.spacingM),

            ..._buildTopicToggles(),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildTopicToggles() {
    final topics = {
      'goals_and_progress': {
        'title': 'Goals and Progress',
        'subtitle': 'Updates on your goals and progress',
        'icon': Icons.outlined_flag,
      },
      'guided_path_updates': {
        'title': 'Guided Path Updates',
        'subtitle': 'Updates on your guided paths and progress',
        'icon': Icons.route,
      },
      'system_announcements': {
        'title': 'System Announcements',
        'subtitle': 'Important updates and announcements',
        'icon': Icons.announcement,
      },
      'weekly_insights': {
        'title': 'Weekly Insights',
        'subtitle': 'Weekly progress summaries and insights',
        'icon': Icons.insights,
      },
    };

    final widgets = <Widget>[];

    for (final entry in topics.entries) {
      final topicKey = entry.key;
      final topicData = entry.value;
      final isEnabled = _modifiedPreferences!.isTopicEnabled(topicKey);

      widgets.add(
        _buildToggleTile(
          title: topicData['title'] as String,
          subtitle: topicData['subtitle'] as String,
          value: isEnabled,
          onChanged: (value) {
            _updatePreferences(
              _modifiedPreferences!.setTopicEnabled(topicKey, value),
            );
          },
          icon: topicData['icon'] as IconData,
        ),
      );

      if (entry.key != topics.keys.last) {
        widgets.add(const Divider());
      }
    }

    return widgets;
  }

  Widget _buildToggleTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    required IconData icon,
  }) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: Icon(icon, color: context.colorScheme.primary),
      title: Text(title, style: context.textTheme.titleMedium),
      subtitle: Text(
        subtitle,
        style: context.textTheme.bodyMedium?.copyWith(
          color: context.colorScheme.onSurfaceVariant,
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeThumbColor: context.colorScheme.primary,
      ),
    );
  }
}
